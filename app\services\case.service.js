import * as fs from "fs";
import path from "path";
import pool from "../config/db.js";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "#middlewares/ErrorClass.js";
import { getCountryCode, runQuery } from "#utils";
import * as smsService from "./sms.service.js";
import {
  getQuestionaire,
  getCountryByUserId,
  getTimeInTimezone,
  convertCountryNameToISOCode,
} from "./questionaire.service.js";
import { noti_type } from "#middlewares/noti_type.js";
import { getCountNoti } from "./noti.service.js";
import { connectedUsers, notificationsNamespace } from "../../index.js";
import PDFDocument from "pdfkit";
import { getStreamAsBuffer } from "get-stream";
import { fileURLToPath } from "url";
import { userService } from "#services";
import { ans_type } from "#middlewares/ans_type.js";
import { roles } from "#middlewares/roles.js";
import { format } from "date-fns";
import { insertModalAnswer } from "./modal.service.js";
import { getModal } from "./modal.service.js";
import axios from "axios";
import schedule from "node-schedule";
import { source } from "#middlewares/source_type.js";
import { s3Client } from "../config/s3.js";
import { GetObjectCommand, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { S3SyncClient } from "s3-sync-client";
import { callMeMail } from "../mails/callMeMail.js";
import ExcelJS from "exceljs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const generateId = () => {
  let d = new Date().getTime(),
    d2 =
      (typeof performance !== "undefined" &&
        performance.now &&
        performance.now() * 1000) ||
      0;
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    let r = Math.random() * 16;
    if (d > 0) {
      r = (d + r) % 16 | 0;
      d = Math.floor(d / 16);
    } else {
      r = (d2 + r) % 16 | 0;
      d2 = Math.floor(d2 / 16);
    }
    return (c == "x" ? r : (r & 0x7) | 0x8).toString(16);
  });
};

export const checkConnectPMS = async (lf_id) => {
  let con = await pool.getConnection();
  const check_connect = await runQuery(
    con,
    "SELECT isConnected FROM law_firm WHERE lf_id = ?",
    [lf_id]
  );
  return check_connect[0]?.isConnected;
};
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"); // $& means the whole matched string
}
export const createCase = async (
  case_name,
  desc,
  obj,
  qtn_id,
  user_id,
  assigned_by,
  case_id_pms,
  token_
) => {
  for (let key in token_) {
    obj = findAndReplace(obj, key, token_[key]);
  }

  let con = await pool.getConnection();
  try {
    //grant permission client
    let user = await runQuery(
      con,
      "SELECT email, role FROM users WHERE user_id = ?",
      [assigned_by]
    );
    let check = await runQuery(
      con,
      "Select * from `cases` where user_id = ? and qtn_id = ?",
      [user_id, qtn_id]
    );
    if (check.length > 0) {
      if (check[0]?.status != 7) {
        return { case_id: check[0]?.id, check: 2 };
      } else {
        return { case_id: check[0]?.id, check: 0 };
      }
    }
    if (obj?.status == 2 || obj?.status == 3) {
      throw ErrorHandler.badRequestError("Questionaire is not active");
    }
    let case_id = await createCaseFromJson(
      obj,
      case_name,
      desc,
      user_id,
      qtn_id,
      assigned_by,
      case_id_pms
    );
    //grant permission client
    if (user[0].role != roles.LawfirmSuperAdmin) {
      await runQuery(
        con,
        "INSERT INTO grant_permission_case(case_id, email) VALUES (?, ?)",
        [case_id, user[0].email]
      );
    }
    return { case_id: case_id, check: 1 };
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getAllCase = async (
  page,
  size = 10,
  keyword = null,
  status,
  tab,
  user_id = null,
  lf_id = null,
  email = null
) => {
  let con = await pool.getConnection();
  try {
    let case_, caseCount;
    let searchParam = user_id || email;
    if (keyword == null || keyword == "" || keyword == undefined) {
      if (tab == 1) {
        var sql = fs.readFileSync("app/sql/getAllCase.sql").toString();
        case_ = await runQuery(con, sql, [
          status,
          lf_id,
          (page - 1) * size,
          size * 1,
        ]);
        var sqlcount = fs
          .readFileSync("app/sql/getAllCaseCount.sql")
          .toString();
        caseCount = await runQuery(con, sqlcount, [status, lf_id]);
      } else {
        var sql = fs
          .readFileSync(
            user_id
              ? "app/sql/getAllCaseClient.sql"
              : "app/sql/getAllCaseClientPMS.sql"
          )
          .toString();
        case_ = await runQuery(con, sql, [
          searchParam,
          status,
          (page - 1) * size,
          size * 1,
        ]);
        var sqlcount = fs
          .readFileSync(
            user_id
              ? "app/sql/getAllCaseClientCount.sql"
              : "app/sql/getAllCaseClientCountPMS.sql"
          )
          .toString();
        caseCount = await runQuery(con, sqlcount, [searchParam, status]);
      }
    } else {
      if (tab == 1) {
        var sql = fs.readFileSync("app/sql/getAllCaseKeyword.sql").toString();
        case_ = await runQuery(con, sql, [
          lf_id,
          status,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          (page - 1) * size,
          size * 1,
        ]);
        var sqlcount = fs
          .readFileSync("app/sql/getAllCaseCountKeyword.sql")
          .toString();
        caseCount = await runQuery(con, sqlcount, [
          lf_id,
          status,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
        ]);
      } else {
        var sql = fs
          .readFileSync(
            user_id
              ? "app/sql/getAllCaseClientKeyword.sql"
              : "app/sql/getAllCaseClientPMSKeyword.sql"
          )
          .toString();
        case_ = await runQuery(con, sql, [
          searchParam,
          status,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          (page - 1) * size,
          size * 1,
        ]);
        var sqlcount = fs
          .readFileSync(
            user_id
              ? "app/sql/getAllCaseClientCountKeyword.sql"
              : "app/sql/getAllCaseClientCountPMSKeyword.sql"
          )
          .toString();
        caseCount = await runQuery(con, sqlcount, [
          searchParam,
          status,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
          `%${keyword}%`,
        ]);
      }
    }
    let count = caseCount[0]["COUNT(*)"];
    return { case: case_, count };
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getDetailCaseAnswerById = async (id) => {
  let case_ = await getCase(id);
  let ans = await extractAnswers(case_);
  return { ans, status: case_.status };
};

export const getCase = async (id) => {
  let con = await pool.getConnection();

  try {
    // Get all case data with optimized JOIN query
    var sql = fs.readFileSync("app/sql/getCaseWithData.sql").toString();
    let caseData = await runQuery(con, sql, [id]);

    if (caseData.length == 0) {
      return [];
    }

    // Get conditions and answers separately
    var conditionsSql = fs.readFileSync("app/sql/getCaseConditions.sql").toString();
    let conditions = await runQuery(con, conditionsSql, [id]);

    var answersSql = fs.readFileSync("app/sql/getCaseAnswers.sql").toString();
    let answers = await runQuery(con, answersSql, [id]);

    // Process the flattened data into hierarchical structure
    let ans = {
      id: id,
      lf_org_name: caseData[0]?.lf_org_name,
      phone_number: caseData[0]?.phone_number,
      name: caseData[0]?.case_name,
      status: caseData[0]?.case_status,
      description: caseData[0]?.case_description,
      created_at: caseData[0]?.created_at,
      qtn_id: caseData[0]?.qtn_id,
      lf_id: caseData[0]?.lf_id,
      user_id: caseData[0]?.user_id,
      case_id_pms: caseData[0]?.case_id_pms,
      assigned_by: caseData[0]?.assigned_by,
      groups: [],
      conditions: []
    };

    // Group the data by group_id, question_id while preserving order
    let groupsMap = new Map();
    let questionsMap = new Map();
    let groupOrder = []; // Track group order
    let questionOrder = new Map(); // Track question order per group

    for (const row of caseData) {
      if (!row.group_id) continue;

      // Process group
      if (!groupsMap.has(row.group_id)) {
        groupsMap.set(row.group_id, {
          id: row.group_id,
          name: row.group_name,
          tooltips: row.group_tooltips,
          linkedTo: row.group_answer_id ? row.group_answer_id.split(", ") : null,
          conditions: null,
          questions: []
        });
        groupOrder.push(row.group_id); // Track order
        questionOrder.set(row.group_id, []); // Initialize question order for this group
      }

      // Process question
      if (row.question_id && !questionsMap.has(row.question_id)) {
        let question = {
          id: row.question_id,
          name: row.question_name,
          description: row.question_text,
          answer_type: row.answer_type,
          files: row.question_files ? JSON.parse(row.question_files) : null,
          required: row.required,
          tooltips: row.question_tooltips,
          selectAnswerTable: row.selectAnswerTable,
          conditions: null,
          linkedTo: row.parent_answer ? row.parent_answer.split(", ") : null,
          answers: {
            id: row.ans_id,
            name: row.answer_name,
            modal_id: row.modal_id,
            modal_type: row.modal_type,
            expandable: row.expandable,
            content: null
          }
        };

        // Handle answer content
        if (row.answer_content != null) {
          question.answers.content = row.answer_content;
        } else {
          // Collect all answers for this question
          let answersContent = [];
          for (const dataRow of caseData) {
            if (dataRow.question_id === row.question_id && dataRow.answers_id) {
              answersContent.push({
                id: dataRow.answers_id,
                name: dataRow.answers_name,
                description: dataRow.answers_text
              });
            }
          }
          question.answers.content = answersContent.length > 0 ? answersContent : null;
        }

        questionsMap.set(row.question_id, question);
        questionOrder.get(row.group_id).push(row.question_id); // Track question order
      }
    }

    // Build groups array in the correct order
    ans.groups = [];
    for (const groupId of groupOrder) {
      let group = groupsMap.get(groupId);
      // Add questions in the correct order
      group.questions = [];
      for (const questionId of questionOrder.get(groupId)) {
        if (questionsMap.has(questionId)) {
          group.questions.push(questionsMap.get(questionId));
        }
      }
      ans.groups.push(group);
    }

    // Process conditions
    for (const index in conditions) {
      let temp = JSON.parse(conditions[index]?.content);
      ans.conditions[index] = temp;
    }

    // Process answers
    for (let answer of answers) {
      if (answer.parse == 1) {
        answer.answer = JSON.parse(answer.answer);
      }
    }
    ans.answer = answers;

    return ans;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const deleteCase = async (id) => {
  let retryCount = 0;
  while (retryCount < 5) {
    let con = await pool.getConnection();

    try {
      await con.beginTransaction();

      let groups = await runQuery(
        con,
        "Select * from `groups` where tem_id = ?",
        [id]
      );
      for (const index in groups) {
        let questions = await runQuery(
          con,
          "Select * from `questions` where gr_id = ?",
          [groups[index]?.id]
        );
        for (const j in questions) {
          let answer = await runQuery(
            con,
            "Select * from `answer` where ques_id = ?",
            [questions[j]?.id]
          );
          let answers = await runQuery(
            con,
            "Select * from `answers` where ques_id = ?",
            [answer[0]?.ans_id]
          );
          for (const k in answers) {
            await runQuery(
              con,
              "Update `answers` set ques_id = NULL where id = ?",
              [answers[k]?.id]
            );
          }
          await runQuery(
            con,
            "update `questions` set parent_answer = NULL ,gr_id = NULL where id = ?",
            [questions[j]?.id]
          );
          await runQuery(
            con,
            "update `answer` set ques_id = NULL  where id = ?",
            [answer[0]?.id]
          );
        }
        await runQuery(
          con,
          "update `groups` set answer_id = NULL, tem_id = NULL where id = ?",
          [groups[index]?.id]
        );
      }
      await runQuery(con, "Delete from `answer` where ques_id is NULL");
      await runQuery(con, "Delete from `answers` where ques_id is NULL");
      await runQuery(con, "Delete from `questions` where gr_id is NULL");
      await runQuery(con, "Delete from `groups` where tem_id is NULL");
      await runQuery(con, "Delete from `cases` where id = ?", id);
      await runQuery(con, "Delete from `conditions` where template_id = ?", id);
      await runQuery(con, "Delete from `answered` where case_id = ?", id);
      await con.commit();
      return 1;
    } catch (error) {
      if (error.code === "ER_LOCK_DEADLOCK") {
        retryCount++;
        await con.rollback();
        await new Promise((r) => setTimeout(r, 200));
      } else {
        await con.rollback();
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    } finally {
      con.destroy();
    }
  }
};
function findAndReplace(obj, findStr, replaceStr) {
  if (replaceStr == null) {
    replaceStr = "";
  }
  if (typeof obj === "object" && obj !== null) {
    if (Array.isArray(obj)) {
      obj.forEach((item, index) => {
        obj[index] = findAndReplace(item, findStr, replaceStr);
      });
    } else {
      for (let key in obj) {
        obj[key] = findAndReplace(obj[key], findStr, replaceStr);
      }
    }
  } else if (typeof obj === "string") {
    return obj.replace(new RegExp(escapeRegExp(findStr), "g"), replaceStr);
  }
  return obj;
}
function replaceQuestionIds(jsonData, id_list) {
  function traverse(data) {
    if (Array.isArray(data)) {
      for (let item of data) {
        traverse(item);
      }
    } else if (typeof data === "object") {
      if (data.type === "question" && data.questionId) {
        data.questionId = id_list[data.questionId];
        if (Array.isArray(data.value)) {
          let newAnswer = [];
          for (let answer of data.value) {
            newAnswer.push(id_list[answer]);
          }
          data.value = newAnswer;
        } else {
          if (id_list[data.value]) {
            data.value = id_list[data.value];
          }
        }
      }
      if (data.children) {
        for (let child of data.children) {
          traverse(child);
        }
      }
    }
  }

  traverse(jsonData.list);

  return jsonData;
}
export const updateCase = async (case_id, updateData, user_id) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();

    // Build dynamic update query based on provided data
    const allowedFields = ['case_name', 'desc', 'status'];
    const updateFields = [];
    const updateValues = [];

    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        updateFields.push(`${field} = ?`);
        updateValues.push(updateData[field]);
      }
    }

    if (updateFields.length === 0) {
      throw ErrorHandler.badRequestError("No valid fields to update");
    }

    // Add update metadata
    updateFields.push('update_at = NOW()', 'update_by = ?');
    updateValues.push(user_id);
    updateValues.push(case_id);

    const sql = `UPDATE cases SET ${updateFields.join(', ')} WHERE id = ?`;

    const result = await runQuery(con, sql, updateValues);

    if (result.affectedRows === 0) {
      throw ErrorHandler.badRequestError("Case not found or no changes made");
    }

    await con.commit();
    return { success: true, affectedRows: result.affectedRows };
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

// Keep the old updateCase function for backward compatibility
export const updateCaseFromJson = async (obj, user_id) => {
  try {
    await deleteCase(obj.id);
    await createCaseFromJson(obj, user_id);
    return 1;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  }
};
//===================================================================
export const createCaseFromJson = async (
  obj,
  case_name,
  desc,
  user_id,
  qtn_id,
  assigned_by,
  case_id_pms
) => {
  let retryCount = 0;
  while (retryCount < 5) {
    let con = await pool.getConnection();
    try {
      await createLink(obj);
      let id_list = [];
      var sql = fs.readFileSync("app/sql/createCaseWithId.sql").toString();
      var case_id = generateId();
      id_list[obj.id] = case_id;
      obj.id = case_id;
      let user = await runQuery(
        con,
        "Select u.email,ui.title, ui.first_name, ui.middle_name, ui.last_name, ui.dob, ui.gender,ui.home_phone,ui.mb_phone,ui.wrk_phone,ui.adr_1,ui.adr_2,ui.adr_3,ui.town,ui.country,ui.post_code from user_info ui JOIN users u ON ui.user_id = u.user_id where ui.user_id = ?",
        [user_id]
      );
      if (user.length == 0)
        throw ErrorHandler.badRequestError("User not found");
      for (let key in user[0]) {
        findAndReplace(obj, `{{${key}}}`, user[0][key]);
      }
      if (case_id_pms != null && case_id_pms != "") {
        let activePiecesWebhookUrl = process.env.MIDDELWARE_CASE_DATA;
        let result = await axios.post(activePiecesWebhookUrl, {
          case_id: case_id_pms,
          lf_id: obj.lf_id,
        });
        for (let key in result.data.data) {
          console.log(key);
          console.log(typeof result.data.data[key]);
          for (let value in result.data.data[key]) {
            findAndReplace(
              obj,
              `{{${key}.${value}}}`,
              result.data.data[key][value]
            );
          }
        }
      }
      await con.beginTransaction();
      await runQuery(con, sql, [
        case_id,
        case_name,
        desc,
        4,
        assigned_by,
        user_id,
        obj.price,
        qtn_id,
        obj.lf_id,
        case_id_pms,
        null,
        source.Internal,
      ]);
      for (const group of obj?.groups) {
        var groupId = generateId();
        id_list[group.id] = groupId;
        group.id = groupId;
        var sql = fs.readFileSync("app/sql/createGroup.sql").toString();
        await runQuery(con, sql, [
          groupId,
          group.name,
          null,
          case_id,
          group.tooltips,
        ]);
        for (const question of group?.questions) {
          var questionId = generateId();
          var ansId = generateId();
          id_list[question.id] = questionId;
          id_list[question.answers.id] = ansId;
          question.id = questionId;
          question.answers.id = ansId;
          var sql = fs.readFileSync("app/sql/createQuestion.sql").toString();
          await runQuery(con, sql, [
            questionId,
            question.name,
            question.description,
            question.answer_type,
            null,
            null,
            question.required,
            question.selectAnswerTable,
            question.tooltips,
            JSON.stringify(question.files) ?? "",
          ]);
          await runQuery(
            con,
            "INSERT INTO `answer` (`ans_id`, `name`, `ans_type`, `ques_id`,`modal_id`,`modal_type`, `expandable`) VALUES ( ?, ?, ?, ?, ?, ?, ?)",
            [
              ansId,
              question.answers.name,
              question.answer_type,
              null,
              question.answers.modal_id,
              question.answers.modal_type,
              question.answers.expandable,
            ]
          );
          if (question?.answers.content != null)
            if (typeof question?.answers.content == "string") {
              await runQuery(
                con,
                "INSERT INTO `answered` (`ques_id`, `case_id`, `answer`) VALUES (?, ?, ?)",
                [questionId, case_id, question?.answers.content]
              );
            } else {
              for (const answer of question?.answers.content) {
                var answerId = generateId();
                id_list[answer.id] = answerId;
                answer.id = answerId;
                var sql = fs
                  .readFileSync("app/sql/createAnswer.sql")
                  .toString();
                await runQuery(con, sql, [
                  answerId,
                  answer.name,
                  answer.description,
                  null,
                ]);
              }
            }
        }
      }
      for (const group of obj?.groups) {
        group?.linkedTo?.forEach((item, index) => {
          group.linkedTo[index] = id_list[item];
        });
        await runQuery(
          con,
          "Select * from `groups` where id = ? for update; update `groups` set answer_id = ? where id = ?",
          [
            group.id,
            typeof group?.linkedTo?.join(", ") !== "undefined"
              ? group?.linkedTo?.join(", ")
              : null,
            group.id,
          ]
        );
        for (const question of group?.questions) {
          question?.linkedTo?.forEach((item, index) => {
            question.linkedTo[index] = id_list[item];
          });
          await runQuery(
            con,
            "select * from `questions` where id =? for update;update `questions` set parent_answer = ?,gr_id = ? where id = ?",
            [
              question.id,
              typeof question?.linkedTo?.join(", ") !== "undefined"
                ? question?.linkedTo?.join(", ")
                : null,
              group.id,
              question.id,
            ]
          );
          await runQuery(
            con,
            "select * from `answer` where ans_id =? for update; update `answer` set ques_id = ? where ans_id = ?",
            [question.answers.id, question.id, question.answers.id]
          );
          if (question?.answers.content != null)
            if (typeof question?.answers.content != "string") {
              for (const answers of question?.answers.content) {
                await runQuery(
                  con,
                  "select * from `answers` where id =? for update; update `answers` set ques_id = ? where id = ?",
                  [answers.id, question.answers.id, answers.id]
                );
              }
            }
        }
      }
      for (let conditions of obj?.conditions) {
        let new_con = replaceQuestionIds(conditions, id_list);
        conditions.linkTo = id_list[conditions.linkTo];
        if (conditions?.repeatedBy)
          conditions.repeatedBy = id_list[conditions.repeatedBy];
        await runQuery(
          con,
          "INSERT INTO `conditions`(template_id,content) VALUES (?,?)",
          [case_id, JSON.stringify(new_con)]
        );
      }
      await con.commit();
      return case_id;
    } catch (error) {
      if (error.code === "ER_LOCK_DEADLOCK") {
        retryCount++;
        await con.rollback();
        await new Promise((r) => setTimeout(r, 200));
      } else {
        await con.rollback();
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    } finally {
      con.destroy();
    }
  }
};

export const updateCaseStatus = async (status, id, user_id) => {
  let retryCount = 0;
  while (retryCount < 5) {
    let con = await pool.getConnection();

    try {
      await con.beginTransaction();
      let cur_status = await runQuery(
        con,
        "Select status from `cases` where id = ?",
        [id]
      );
      // if (cur_status[0]?.status == 7) {
      //   throw ErrorHandler.badRequestError("Case already submitted");
      // }
      await runQuery(
        con,
        "Update `cases` set status = ? , update_at = Now() , update_by = ?  where id = ?",
        [status, user_id, id]
      );
      await con.commit();
      return 1;
    } catch (error) {
      if (error.code === "ER_LOCK_DEADLOCK") {
        retryCount++;
        await con.rollback();
        await new Promise((r) => setTimeout(r, 200));
      } else {
        await con.rollback();
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    } finally {
      con.destroy();
    }
  }
};

function extractQuestionIds(data) {
  let questionIds = [];
  function traverse(obj) {
    if (typeof obj === "object" && obj !== null) {
      if (obj.hasOwnProperty("questionId")) {
        questionIds.push(obj["questionId"]);
      }
      for (let key in obj) {
        traverse(obj[key]);
      }
    }
  }
  traverse(data);
  return questionIds;
}

function mergeListsUnique(list1, list2) {
  let mergedSet = new Set([...list1, ...list2]);
  return Array.from(mergedSet);
}

function alterValueById(idToMatch, newValue, jsonData) {
  for (let group of jsonData.groups) {
    if (group.id === idToMatch) {
      if (newValue == null) return;
      if (group.linkedTo != null) {
        group.linkedTo = mergeListsUnique(group.linkedTo, newValue);
      } else group.linkedTo = newValue;
      return;
    }
    for (let question of group.questions) {
      if (question.id === idToMatch) {
        if (newValue == null) return;
        if (question.linkedTo != null) {
          question.linkedTo = mergeListsUnique(question.linkedTo, newValue);
        } else question.linkedTo = newValue;
      }
    }
  }
}

export const createLink = async (obj) => {
  try {
    for (const groups of obj?.groups) {
      groups.linkedTo = null;
      for (const questions of groups?.questions) {
        questions.linkedTo = null;
      }
    }
    for (const conditions of obj?.conditions) {
      let temp = extractQuestionIds(conditions);
      alterValueById(conditions.linkTo, temp, obj);
    }
    return;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  }
};

export const updateAnswer = async (obj, user_id) => {
  let retryCount = 0;
  while (retryCount < 5) {
    let con = await pool.getConnection();
    try {
      await con.beginTransaction();
      for (let ans of obj?.answer) {
        // let check_ans_type = await runQuery(
        //   con,
        //   "SELECT answer_type FROM questions WHERE id = ?",
        //   [ans.ques_id]
        // );
        // if (
        //   check_ans_type[0].answer_type == 10 ||
        //   check_ans_type[0].answer_type == 11
        // ) {
        //   let ans_obj = JSON.parse(ans.answer);
        //   for (const [field, content] of Object.entries(ans_obj)) {
        //     await runQuery(
        //       con,
        //       "INSERT INTO modal_answer (ques_id, field, content) VALUES (?, ?, ?)",
        //       [ans.ques_id, field, content]
        //     );
        //   }
        // }
        let parse = 0;
        if (typeof ans.answer !== "String") {
          parse = 1;
          ans.answer = JSON.stringify(ans.answer);
        }
        await runQuery(
          con,
          "INSERT INTO `answered` (ques_id,case_id) SELECT * from (SELECT ? , ?) AS tmp WHERE NOT EXISTS (SELECT ques_id from `answered` WHERE ques_id = ? and case_id = ?) ;Update `answered` set answer = ? , parse = ? where ques_id = ? AND case_id = ?",
          [
            ans.ques_id,
            obj.id,
            ans.ques_id,
            obj.id,
            ans.answer,
            parse,
            ans.ques_id,
            obj.id,
          ]
        );
      }
      await updateCaseStatus(6, obj.id, user_id);
      await con.commit();
      return;
    } catch (error) {
      if (error.code === "ER_LOCK_DEADLOCK") {
        retryCount++;
        await con.rollback();
        await new Promise((r) => setTimeout(r, 200));
      } else {
        await con.rollback();
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    } finally {
      con.destroy();
    }
  }
};

export const updateAnswerSubmitted = async (obj, user_id) => {
  let retryCount = 0;
  while (retryCount < 5) {
    let con = await pool.getConnection();
    try {
      await con.beginTransaction();

      // Track modifications for email notification
      const modifications = [];

      for (let ans of obj?.answer) {
        // Get original answer for comparison
        const originalAnswer = await runQuery(
          con,
          "SELECT answer, parse FROM answered WHERE ques_id = ? AND case_id = ?",
          [ans.ques_id, obj.id]
        );

        let parse = 0;
        if (typeof ans.answer !== "String") {
          parse = 1;
          ans.answer = JSON.stringify(ans.answer);
        }

        // Track modification if answer exists and is different
        if (originalAnswer.length > 0) {
          let originalValue = originalAnswer[0].answer;
          if (originalAnswer[0].parse == 1) {
            try {
              originalValue = JSON.parse(originalValue);
            } catch (e) {
              // Keep as string if parsing fails
            }
          }

          let newValue = ans.answer;
          if (parse == 1) {
            try {
              newValue = JSON.parse(newValue);
            } catch (e) {
              // Keep as string if parsing fails
            }
          }

          // Compare values (convert to string for comparison)
          if (JSON.stringify(originalValue) !== JSON.stringify(newValue)) {
            modifications.push({
              ques_id: ans.ques_id,
              originalAnswer: typeof originalValue === 'object' ? JSON.stringify(originalValue) : originalValue,
              newAnswer: typeof newValue === 'object' ? JSON.stringify(newValue) : newValue
            });
          }
        }

        await runQuery(
          con,
          "INSERT INTO `answered` (ques_id,case_id) SELECT * from (SELECT ? , ?) AS tmp WHERE NOT EXISTS (SELECT ques_id from `answered` WHERE ques_id = ? and case_id = ?) ;Update `answered` set answer = ? , parse = ? where ques_id = ? AND case_id = ?",
          [
            ans.ques_id,
            obj.id,
            ans.ques_id,
            obj.id,
            ans.answer,
            parse,
            ans.ques_id,
            obj.id,
          ]
        );
      }

      await con.commit();
      return modifications;
    } catch (error) {
      if (error.code === "ER_LOCK_DEADLOCK") {
        retryCount++;
        await con.rollback();
        await new Promise((r) => setTimeout(r, 200));
      } else {
        await con.rollback();
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    } finally {
      con.destroy();
    }
  }
};

/**
 * Send case modification notification email to client
 */
export const sendCaseModificationNotification = async (case_id, modifications, requesting_user) => {
  if (!modifications || modifications.length === 0) {
    return; // No modifications to notify about
  }

  let con = await pool.getConnection();
  try {
    // Get case and user information
    const caseInfo = await runQuery(
      con,
      `SELECT c.id, c.case_name, c.lf_id, u.email, ui.first_name, ui.last_name
       FROM cases c
       JOIN users u ON c.user_id = u.user_id
       JOIN user_info ui ON u.user_id = ui.user_id
       WHERE c.id = ?`,
      [case_id]
    );

    if (caseInfo.length === 0) {
      console.log('Case not found for modification notification');
      return;
    }

    const case_data = caseInfo[0];

    // Get question details for modifications
    const modificationDetails = [];
    for (const mod of modifications) {
      const questionInfo = await runQuery(
        con,
        "SELECT text as question_text FROM questions WHERE id = ?",
        [mod.ques_id]
      );

      if (questionInfo.length > 0) {
        modificationDetails.push({
          question: questionInfo[0].question_text,
          originalAnswer: mod.originalAnswer,
          newAnswer: mod.newAnswer
        });
      }
    }

    // Send modification notification email using smart wrapper
    const { sendCaseModificationMailSmart } = await import('./mail.service.js');
    await sendCaseModificationMailSmart({
      email: case_data.email,
      first_name: case_data.first_name,
      case_id: case_id,
      modifications: modificationDetails,
      lf_id: case_data.lf_id,
      path: null,
      bgColor: null,
      textColor: null,
      title: null,
      header: null,
      body: null,
      footer: null,
      logo: null,
      user: null
    }, requesting_user);

    console.log(`Case modification notification sent to ${case_data.email} for case ${case_id}`);

  } catch (error) {
    console.error('Error sending case modification notification:', error);
  } finally {
    con.destroy();
  }
};

export const getCaseByQuestionaireId = async (id) => {
  let con = await pool.getConnection();
  try {
    let case_ = await runQuery(
      con,
      "Select distinct ui.user_id, ui.email  from `cases` c JOIN users ui on ui.user_id = c.user_id  where qtn_id = ?",
      [id]
    );
    if (case_.length == 0) {
      await con.commit();
      return [];
    }
    return case_;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getCaseSumByUserId = async (page, size = 10, keyword, lf_id) => {
  let con = await pool.getConnection();
  try {
    var sql = fs.readFileSync("app/sql/getCaseSumary.sql").toString();
    let case_ = await runQuery(con, sql, [
      lf_id,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      (page - 1) * size,
      size * 1,
    ]);

    var sqlcount = fs.readFileSync("app/sql/getCaseSumaryCount.sql").toString();
    let caseCount = await runQuery(con, sqlcount, [
      lf_id,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
      `%${keyword}%`,
    ]);
    let count = caseCount[0]["COUNT(*)"];
    return { case: case_, count };
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const getCaseByUserId = async (id) => {
  let con = await pool.getConnection();
  try {
    let case_ = await runQuery(con, "Select * from `cases` where user_id = ?", [
      id,
    ]);
    if (case_.length == 0) {
      await con.commit();
      return null;
    }
    return case_;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateInvoice = async (id) => {
  let retryCount = 0;
  while (retryCount < 5) {
    let con = await pool.getConnection();

    try {
      await con.beginTransaction();
      let case_ = await runQuery(con, "SELECT * FROM cases WHERE id = ?", [id]);
      if (case_.length < 1) {
        await con.commit();
        return [];
      }
      await runQuery(
        con,
        "INSERT INTO `charges_history` (`issued_date`, `service_info`, `cost`, `lf_id`) VALUES (NOW(),?, ?, ?)",
        [`${case_[0]?.case_name}`, case_[0]?.price, case_[0]?.lf_id]
      );
      await con.commit();
      return;
    } catch (error) {
      if (error.code === "ER_LOCK_DEADLOCK") {
        retryCount++;
        await con.rollback();
        await new Promise((r) => setTimeout(r, 200));
      } else {
        await con.rollback();
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    } finally {
      con.destroy();
    }
  }
};

export const callMe = async (case_id, phone_number, best_time) => {
  let retryCount = 0;
  while (retryCount < 5) {
    let con = await pool.getConnection();
    try {
      await con.beginTransaction();
      let user = await runQuery(
        con,
        "SELECT user_id, created_by, lf_id, party_info_id FROM cases WHERE id = ?",
        [case_id]
      );
      if (user[0].user_id) {
        let phone = await runQuery(
          con,
          "SELECT ui.title, ui.first_name FROM users u JOIN user_info ui ON u.user_id = ui.user_id WHERE u.user_id = ?",
          [user[0]?.user_id]
        );
        var message = `You have received a callback request from ${phone[0]?.title} ${phone[0]?.first_name} at the phone number ${phone_number}. The preferred callback time is ${best_time}.`;
      } else {
        let party = await runQuery(
          con,
          "SELECT first_name FROM party_info WHERE id = ?",
          [user[0]?.party_info_id]
        );
        var message = `You have received a callback request from ${party[0]?.first_name} at the phone number ${phone_number}. The preferred callback time is ${best_time}`;
      }
      const receivers_mail = await runQuery(
        con,
        "SELECT email FROM grant_permission_case WHERE case_id = ? UNION ALL SELECT email FROM users WHERE lf_id = ? AND role = 5",
        [case_id, user[0]?.lf_id]
      );
      let sql = `INSERT INTO notification (message, created_at, status, user_id, source, type) VALUES (?, ?, 1, ?, ?, ?)`;
      let country = await getCountryByUserId(user[0]?.user_id);
      var time = getTimeInTimezone(convertCountryNameToISOCode(country));
      const receivers = await runQuery(
        con,
        `SELECT DISTINCT user_id 
         FROM users 
         WHERE (lf_id = ? AND (role = 2 OR role = 5)) 
         OR user_id IN (?, ?)`,
        [user[0].lf_id, user[0].created_by, user[0].user_id]
      );

      const uniqueReceivers = [...new Set(receivers.map((r) => r.user_id))];

      const notificationPromises = uniqueReceivers.map(async (userId) => {
        return await runQuery(con, sql, [
          message,
          time,
          userId,
          case_id,
          noti_type.CallMe,
        ]);
      });

      const notificationResults = await Promise.all(notificationPromises);

      const emailPromises = receivers_mail.map(async (receiver) => {
        const userInfo = await runQuery(
          con,
          "SELECT u.user_id, ui.first_name FROM users u JOIN user_info ui ON u.user_id = ui.user_id WHERE u.email = ?",
          [receiver.email]
        );

        return callMeMail(
          receiver.email,
          userInfo[0]?.first_name,
          phone_number,
          best_time
        );
      });
      await Promise.all(emailPromises);

      let result = await runQuery(con, sql, [
        message,
        time,
        user[0]?.created_by,
        case_id,
        noti_type.CallMe,
      ]);
      await con.commit();
      for (let i = 0; i < uniqueReceivers.length; i++) {
        const receiverId = uniqueReceivers[i];

        let count = await getCountNoti(receiverId);

        if (connectedUsers[receiverId]) {
          notificationsNamespace
            .to(connectedUsers[receiverId]?.id)
            .emit("notification", {
              message: message,
              case_id: case_id,
              type: noti_type.CallMe,
              time: time,
              count: count[0]?.COUNT,
              status: 1,
              noti_id: notificationResults[i].insertId,
            });
        }
      }
      return;
    } catch (error) {
      if (error.code === "ER_LOCK_DEADLOCK") {
        retryCount++;
        await con.rollback();
        await new Promise((r) => setTimeout(r, 200));
      } else {
        await con.rollback();
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    } finally {
      con.destroy();
    }
  }
};

// export const exportPDFToPMS = async (obj) => {
//   let ans = await extractAnswers(obj);
//   return await generatePDF(obj, ans);
// };

export const downloadAfterSubmit = async (case_id) => {
  let case_ = await getCase(case_id);
  let ans = await extractAnswers(case_);
  return await generatePDF(case_, ans, null);
};

export const downloadPDF = async (obj, role) => {
  // let case_ = await getCase(case_id);
  let ans = await extractAnswers(obj);
  return await generatePDF(obj, ans, role);
};

export const checkPermission = async (case_id, email) => {
  let con = await pool.getConnection();
  try {
    let check = await runQuery(
      con,
      "SELECT COUNT(*) AS COUNT FROM grant_permission_case WHERE case_id = ? AND email = ?",
      [case_id, email]
    );
    return check[0]?.COUNT;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

const generatePDF = async (caseData, answers, role) => {
  try {
    const user = await userService.findUserById(caseData.user_id);
    const doc = new PDFDocument({ margin: 50 });
    doc.registerFont(
      "Bold",
      path.join(__dirname, "../../font/Roboto-Bold.ttf")
    );
    doc.registerFont(
      "Italic",
      path.join(__dirname, "../../font/Roboto-Italic.ttf")
    );
    doc.registerFont(
      "BoldItalic",
      path.join(__dirname, "../../font/Roboto-BoldItalic.ttf")
    );
    doc.registerFont(
      "Light",
      path.join(__dirname, "../../font/Roboto-Light.ttf")
    );
    doc.registerFont(
      "LightItalic",
      path.join(__dirname, "../../font/Roboto-LightItalic.ttf")
    );
    doc.registerFont(
      "Medium",
      path.join(__dirname, "../../font/Roboto-Medium.ttf")
    );
    doc.registerFont(
      "MediumItalic",
      path.join(__dirname, "../../font/Roboto-MediumItalic.ttf")
    );
    doc.registerFont(
      "Regular",
      path.join(__dirname, "../../font/Roboto-Regular.ttf")
    );
    doc.registerFont(
      "Thin",
      path.join(__dirname, "../../font/Roboto-Thin.ttf")
    );
    doc.registerFont(
      "ThinItalic",
      path.join(__dirname, "../../font/Roboto-ThinItalic.ttf")
    );

    doc
      .font(path.join(__dirname, "../../font/Roboto-Black.ttf"))
      .fontSize(25)
      .fillColor("darkblue")
      .text("Client Case Summary", { underline: true, align: "center" })
      .moveDown(0.5);

    if (role == roles.LawfirmSuperAdmin || role == roles.LawfirmAdmin) {
      caseData.case_id_pms
        ? doc
            .font("BoldItalic")
            .fontSize(16)
            .fillColor("green")
            .text(`Case ID: ${caseData.case_id_pms}`, {
              align: "center",
            })
            .moveDown(2.0)
        : doc
            .font("BoldItalic")
            .fontSize(16)
            .fillColor("green")
            .text(`Case ID: ${caseData.id || "Unknown"}`, {
              align: "center",
            })
            .moveDown(2.0);
    }

    if (caseData.description && !caseData.description.startsWith("undefined")) {
      doc
        .font("BoldItalic")
        .fontSize(16)
        .fillColor("green")
        .text(`Description: ${caseData.description}`, {
          align: "center",
        })
        .moveDown(2.0);
    }
    // doc
    //   .font("BoldItalic")
    //   .fontSize(16)
    //   .fillColor("green")
    //   .text(`Case ID: ${caseData.id || "Unknown"}`, {
    //     align: "center",
    //   })
    //   .moveDown(2.0);

    // if (caseData.case_id_pms) {
    //   doc
    //     .font("BoldItalic")
    //     .fontSize(16)
    //     .fillColor("green")
    //     .text(`Case ID: ${caseData.case_id_pms}`, {
    //       align: "center",
    //     })
    //     .moveDown(2.0);
    // }

    doc.font("Bold").fontSize(16).fillColor("black").text(`Organization: `, {
      background: "lightgrey",
      continued: true,
    });
    doc
      .font("Regular")
      .fontSize(16)
      .fillColor("black")
      .text(`${caseData.lf_org_name || "Not provided"}`, {
        background: "lightgrey",
      });

    doc.font("Bold").fontSize(16).fillColor("black").text(`Phone Number: `, {
      background: "lightgrey",
      continued: true,
    });
    doc
      .font("Regular")
      .fontSize(16)
      .fillColor("black")
      .text(`${caseData.phone_number || "Not provided"}`, {
        background: "lightgrey",
      });

    doc.font("Bold").fontSize(16).fillColor("black").text(`Email Client: `, {
      background: "lightgrey",
      continued: true,
    });
    doc
      .font("Regular")
      .fontSize(16)
      .fillColor("black")
      .text(`${user[0]?.email || "Not provided"}`, {
        background: "lightgrey",
      });

    doc.font("Bold").fontSize(16).fillColor("black").text(`Client Name: `, {
      background: "lightgrey",
      continued: true,
    });
    doc
      .font("Regular")
      .fontSize(16)
      .fillColor("black")
      .text(
        `${user[0]?.first_name + " " + user[0]?.last_name || "Not provided"}`,
        {
          background: "lightgrey",
        }
      );
    doc.moveDown(1.0);

    doc
      .strokeColor("grey")
      .lineWidth(1)
      .moveTo(50, doc.y)
      .lineTo(550, doc.y)
      .stroke();
    doc.moveDown(1.0);
    answers.forEach((group) => {
      if (!group.questions.every((q) => q.answer === "")) {
        doc.fontSize(18).fillColor("green").text(group.groupName);
        group.questions.forEach((answer, num = 0) => {
          const moneyRegex =
            /(?<!\$)(\b\d{1,3}(?:,\d{3})*(?:\.\d{2})\b|\b\d+\.\d{2}\b)/g;
          answer.answer = answer.answer.replace(moneyRegex, (match) => {
            const parts = match.split(".");
            const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            const decimalPart = parts[1] ? `.${parts[1]}` : ".00";
            return `£${integerPart}${decimalPart}`;
          });

          if (answer.answer == "") {
            return;
          }
          num++;
          doc
            .fontSize(14)
            .fillColor("maroon")
            .text(`Question ${num}: ${answer.question}`);
          if (typeof answer.answer === "string") {
            try {
              let lines = answer.answer.split("\n");

              lines = lines.map((line) => {
                line = line.split("[-{+?=;;?+};}{@&,])@}[+@=]=?]-").join("");
                if (line.startsWith("Gender: ")) {
                  const genderValue = line.split(": ")[1]?.trim();
                  let displayValue = "Prefer not to say";
                  if (genderValue === "0") {
                    displayValue = "Female";
                  } else if (genderValue === "1") {
                    displayValue = "Male";
                  } else if (genderValue === "2") {
                    displayValue = "Non-binary";
                  }
                  return `Gender: ${displayValue}`;
                }
                return line;
              });

              answer = lines.join("\n");
              doc.fontSize(12).fillColor("black").text(answer);
            } catch (e) {
              // Handle non-JSON answer by just printing it
              doc.fontSize(12).fillColor("black").text(answer.answer);
            }
          } else {
            // doc.fontSize(12).fillColor("black").text("No answer provided");
          }
          doc.moveDown(1.0);
          num++;
        });
        doc.moveDown(1.0);
      }
    });

    doc.end();

    const pdfStream = await getStreamAsBuffer(doc);

    return pdfStream;
  } catch (err) {
    console.error("Pipeline failed:", err);
    throw err;
  }
};

export const grantPermission = async (case_id, email) => {
  let retryCount = 0;
  while (retryCount < 5) {
    let con = await pool.getConnection();
    try {
      let user = await userService.findUser(email);
      let case_info = await getCase(case_id);
      let checkDup = await runQuery(
        con,
        "SELECT COUNT(*) AS COUNT FROM grant_permission_case WHERE case_id = ? and email = ?",
        [case_id, email]
      );
      if (checkDup[0]?.COUNT == 0) {
        if (user.length > 0) {
          if (user[0]?.lf_id == case_info.lf_id) {
            await con.beginTransaction();
            let grtPer = await runQuery(
              con,
              "INSERT INTO grant_permission_case(case_id, email) VALUES (?, ?)",
              [case_id, email]
            );
            // let msg = `You are granted access to case ${case_info.name}`;
            let msg = `You are granted permission to export a new cases. Click to view details.`;
            let country = await getCountryByUserId(user[0]?.user_id);
            let time = getTimeInTimezone(convertCountryNameToISOCode(country));
            let result = await runQuery(
              con,
              "INSERT INTO notification (message, created_at, status, user_id, source, type) VALUES (?, ?, 1, ?, ?, ?)",
              [
                msg,
                time,
                user[0]?.user_id,
                case_id,
                noti_type.GrantedPermission,
              ]
            );
            await con.commit();
            let count = await getCountNoti(user[0]?.user_id);
            if (connectedUsers[user[0]?.user_id]) {
              notificationsNamespace
                .to(connectedUsers[user[0]?.user_id]?.id)
                .emit("notification", {
                  message: msg,
                  case_id: case_id,
                  type: noti_type.GrantedPermission,
                  time: time,
                  count: count[0]?.COUNT,
                  status: 1,
                  noti_id: result.insertId,
                });
            }
            return 1;
          } else {
            return 2;
          }
        } else {
          return 0;
        }
      } else return 3;
    } catch (error) {
      if (error.code === "ER_LOCK_DEADLOCK") {
        retryCount++;
        await con.rollback();
        await new Promise((r) => setTimeout(r, 200));
      } else {
        await con.rollback();
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    } finally {
      con.destroy();
    }
  }
};

export const denyPermission = async (case_id, email) => {
  let retryCount = 0;
  while (retryCount < 5) {
    let con = await pool.getConnection();
    try {
      await con.beginTransaction();
      let user = await userService.findUser(email);
      let case_info = await getCase(case_id);
      let grtPer = await runQuery(
        con,
        "DELETE FROM grant_permission_case WHERE case_id = ? and email = ?",
        [case_id, email]
      );
      // let msg = `Your access to case ${case_info.name} has been revoked.`;
      let msg = `Permission revoked export a cases. Click to view details.`;
      let country = await getCountryByUserId(user[0]?.user_id);
      let time = getTimeInTimezone(convertCountryNameToISOCode(country));
      let result = await runQuery(
        con,
        "INSERT INTO notification (message, created_at, status, user_id, source, type) VALUES (?, ?, 1, ?, ?, ?)",
        [msg, time, user[0]?.user_id, case_id, noti_type.DeniedPermission]
      );
      await con.commit();
      let count = await getCountNoti(user[0]?.user_id);
      if (connectedUsers[user[0]?.user_id]) {
        notificationsNamespace
          .to(connectedUsers[user[0]?.user_id]?.id)
          .emit("notification", {
            message: msg,
            case_id: case_id,
            type: noti_type.DeniedPermission,
            time: time,
            count: count[0]?.COUNT,
            status: 1,
            noti_id: result.insertId,
          });
      }
      if (grtPer.affectedRows > 0) {
        return 1;
      } else return 0;
    } catch (error) {
      if (error.code === "ER_LOCK_DEADLOCK") {
        retryCount++;
        await con.rollback();
        await new Promise((r) => setTimeout(r, 200));
      } else {
        await con.rollback();
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    } finally {
      con.destroy();
    }
  }
};

export const getAllGranted = async (case_id) => {
  let con = await pool.getConnection();
  try {
    let all = await runQuery(
      con,
      "SELECT email FROM grant_permission_case WHERE case_id = ?",
      [case_id]
    );
    return all;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

const extractAnswers = async (caseData) => {
  try {
    const groups = await Promise.all(
      caseData.groups.map(async (group) => {
        const questionsPromises = group.questions.map(async (question) => {
          const answerRecord = caseData.answer.find(
            (ans) => ans.ques_id === question.id
          );

          let answerText = await formatAnswer(
            answerRecord?.answer,
            question.answer_type,
            question.answers.id
          );
          if (answerText === "No answer provided") {
            answerText = "";
          }

          return {
            question: question.description,
            answer: answerText,
          };
        });

        const questions = await Promise.all(questionsPromises);
        return {
          groupName: group.name,
          questions: questions,
        };
      })
    );

    return groups;
  } catch (error) {
    console.error("Error extracting answers:", error);
    throw error;
  }
};

// const formatAnswer = async (answerText, answerType) => {
//   if (!answerText) return "No answer provided";
//   try {
//     if (typeof answerText === "object" && answerType !== ans_type.Checkbox) {
//       return Object.entries(answerText)
//         .map(([key, value]) => {
//           return `Answer ${Number(key) + 1}: ${value}`;
//         })
//         .join("\n");
//     } else if (
//       answerType === ans_type.RadioSet ||
//       answerType === ans_type.DropDownList
//     ) {
//       console.log(await findAnswerTextById(answerText));
//       return await findAnswerTextById(answerText);
//     } else if (answerType === ans_type.Checkbox) {
//       return Object.entries(answerObject)
//         .map(async ([key, value]) => {
//           return `Answer ${Number(key) + 1}: ${value}`;
//         })
//         .join("\n");
//     } else return answerText;
//   } catch (error) {
//     if (
//       answerType === ans_type.RadioSet ||
//       answerType === ans_type.DropDownList
//     ) {
//       return await findAnswerTextById(answerText);
//     } else if (answerType === ans_type.Checkbox) {
//       let results = await Promise.all(
//         Object.entries(answerText).map(async ([key, value]) => {
//           let text = "";
//           let list_ans = [];
//           for (let i = 0; i < value.length; i++) {
//             list_ans.push(await findAnswerTextById(value[i]));
//           }
//           return `Answer ${Number(key) + 1}: ${list_ans.join(", ")}`;
//         })
//       );

//       return results.join("\n");
//     } else return answerText;
//   }
// };

const findAnswerTextById = async (answerId) => {
  let con = await pool.getConnection();
  try {
    let result = await runQuery(con, "SELECT name FROM answers WHERE id = ?", [
      answerId,
    ]);
    return result[0]?.name;
  } catch (error) {
    console.log(error);
  } finally {
    con.destroy();
  }
};

const formatAnswer = async (answerText, answerType, answer_id) => {
  if (!answerText) return "No answer provided";
  try {
    if (typeof answerText === "object") {
      if (answerType === ans_type.Checkbox) {
        let results = await Promise.all(
          Object.entries(answerText).map(async ([key, value]) => {
            if (Array.isArray(value)) {
              let list_ans = await Promise.all(
                value.map(async (id) => {
                  return await findAnswerTextById(id);
                })
              );
              return `Answer ${Number(key) + 1}: ${list_ans.join(", ")}`;
            } else {
              return `Answer ${Number(key) + 1}: ${await findAnswerTextById(
                value
              )}`;
            }
          })
        );
        return results.join("\n");
      } else if (
        answerType === ans_type.RadioSet ||
        answerType === ans_type.DropDownList
      ) {
        let results = await Promise.all(
          Object.entries(answerText).map(async ([key, value]) => {
            let text;
            for (let i = 0; i < value.length; i++) {
              text = await findAnswerTextById(value);
            }
            return `Answer ${Number(key) + 1}: ${text}`;
          })
        );
        return results.join("\n");
      } else if (
        answerType !== ans_type.Modal &&
        answerType !== ans_type.File
      ) {
        const pattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/;
        return Object.entries(answerText)
          .map(([key, value]) => {
            if (pattern.test(value)) {
              return `Answer ${Number(key) + 1}: ${format(
                value,
                "dd/MM/yyyy"
              )}`;
            } else return `Answer ${Number(key) + 1}: ${value}`;
          })
          .join("\n");
      } else if (answerType == ans_type.File) {
        return answerText
          .map((file) => `See attached file: ${file.name}`)
          .join("\n");
      } else {
        let result = await findAnswerModal(answer_id);
        let stringOutput = "[-{+?=;;?+};}{@&,])@}[+@=]=?]-";
        let i = 1;
        for (const item of result) {
          stringOutput += `Modal: ${item.Qname}\n`;
          for (const key in item) {
            if (item[key] === "") continue;
            const pattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/;
            if (key !== "Qname" && item[key] !== undefined) {
              if (pattern.test(item[key]))
                stringOutput += `Question ${i}: ${key} \nAnswer: ${format(
                  item[key],
                  "dd/MM/yyyy"
                )}\n\n`;
              else
                stringOutput += `Question ${i}: ${key} \nAnswer: ${item[key]}\n\n`;
              i++;
            }
          }
          stringOutput += "\n";
        }
        return stringOutput;
      }
    } else {
      if (
        answerType === ans_type.Checkbox ||
        answerType === ans_type.DropDownList ||
        answerType === ans_type.RadioSet
      ) {
        return await findAnswerTextById(answerText);
      } else {
        return answerText;
      }
    }
  } catch (error) {
    throw ErrorHandler.badRequestError(error.message);
  }
};
const findAnswerModal = async (answer_id) => {
  let con = await pool.getConnection();
  try {
    var sql = fs.readFileSync("app/sql/findAnswerModalPDF.sql").toString();
    let extractModal = await runQuery(con, sql, [answer_id]);
    const modal = extractModal[0];
    if (!modal) return;

    const content = JSON.parse(modal.content);
    const answer = JSON.parse(modal.answer);
    let result = [];
    for (let i = 0; i < Object.keys(answer[content[0].id]).length; i++) {
      const obj = { Qname: `${modal.name} ${i + 1}` };
      content.forEach((field) => {
        if (answer[field.id]) {
          obj[field.name] = answer[field.id][i];
        } else {
          obj[field.name] = "";
        }
      });
      result.push(obj);
    }
    return result;
  } catch (error) {
    console.log(error);
  } finally {
    con.destroy();
  }
};
export const getModalAnswer = async (case_id) => {
  let con = await pool.getConnection();
  try {
    let res = {};
    let case_ = await getCase(case_id);
    for (let groups of case_.groups) {
      for (let questions of groups.questions) {
        if (questions.answer_type == 10) {
          let modal = await getModal(questions.answers.modal_id);
          for (let element of modal.content) {
            if (!res.hasOwnProperty(element.name)) {
              res[element.name] = [];
            }
          }
        }
      }
    }
    for (const [key, value] of Object.entries(res)) {
      let temp = await runQuery(
        con,
        "SELECT Distinct answer FROM modal_answer WHERE field = ? and user_id = ?",
        [key, case_.user_id]
      );
      for (let element of temp) {
        res[key].push(element.answer);
      }
    }
    return res;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const updateModalAnswer = async (case_id) => {
  let retryCount = 0;
  while (retryCount < 5) {
    let con = await pool.getConnection();
    try {
      await con.beginTransaction();
      let case_ = await getCase(case_id);
      for (let groups of case_.groups) {
        for (let questions of groups.questions) {
          if (questions.answer_type == 10) {
            let modal = await getModal(questions.answers.modal_id);
            for (let element of modal.content) {
              for (let ans of case_.answer) {
                if (Array.isArray(ans.answer)) {
                  for (let ans_ of ans.answer) {
                    if (ans_.hasOwnProperty(element.id)) {
                      for (let properties in ans_[element.id]) {
                        await runQuery(
                          con,
                          "INSERT INTO modal_answer (user_id, field, answer) SELECT ?, ?, ? WHERE NOT EXISTS ( SELECT 1 FROM modal_answer WHERE user_id = ? AND field = ? AND answer = ? )",
                          [
                            case_.user_id,
                            element.name,
                            ans_[element.id][properties],
                            case_.user_id,
                            element.name,
                            ans_[element.id][properties],
                          ]
                        );
                      }
                    }
                  }
                } else if (typeof ans.answer === "object") {
                  if (ans.answer.hasOwnProperty(element.id)) {
                    for (let properties in ans.answer[element.id]) {
                      if (
                        typeof ans.answer[element.id][properties] === "string"
                      ) {
                        await runQuery(
                          con,
                          "INSERT INTO modal_answer (user_id, field, answer) SELECT ?, ?, ? WHERE NOT EXISTS ( SELECT 1 FROM modal_answer WHERE user_id = ? AND field = ? AND answer = ? )",
                          [
                            case_.user_id,
                            element.name,
                            ans.answer[element.id][properties],
                            case_.user_id,
                            element.name,
                            ans.answer[element.id][properties],
                          ]
                        );
                      } else {
                        let answer =
                          ans.answer[element.id][properties].toString();
                        await runQuery(
                          con,
                          "INSERT INTO modal_answer (user_id, field, answer) SELECT ?, ?, ? WHERE NOT EXISTS ( SELECT 1 FROM modal_answer WHERE user_id = ? AND field = ? AND answer = ? )",
                          [
                            case_.user_id,
                            element.name,
                            answer,
                            case_.user_id,
                            element.name,
                            answer,
                          ]
                        );
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      await con.commit();
    } catch (error) {
      if (error.code === "ER_LOCK_DEADLOCK") {
        retryCount++;
        await con.rollback();
        await new Promise((r) => setTimeout(r, 200));
      } else {
        await con.rollback();
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    } finally {
      con.destroy();
    }
  }
};

export const getAllReceiverSubmitCaseMail = async (lf_id, case_id) => {
  let con = await pool.getConnection();
  try {
    var sql = fs
      .readFileSync("app/sql/getAllReceiverSubmitCaseMail.sql")
      .toString();
    let result = await runQuery(con, sql, [lf_id, case_id, case_id]);
    return result;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

// export const getDataPartyFromPMS = async (obj_user) => {
//   return obj_user;
// };

// export const getDataPMS = async (keyword) => {
//   let con = await pool.getConnection();
//   try {
//     await con.beginTransaction();
//     let result = await runQuery(
//       con,
//       "SELECT * FROM pms_party_data WHERE name LIKE ? OR email LIKE ?",
//       [`%${keyword}%`, `%${keyword}%`]
//     );
//     await con.commit();
//     return result;
//   } catch (error) {
//     await con.rollback();
//     console.log(error);
//     throw ErrorHandler.badRequestError(error.message);
//   } finally {
//     con.destroy();
//   }
// };

export const getCaseIdFromPMS = async (
  name,
  surn,
  keyword,
  filter,
  page,
  lf_id
) => {
  try {
    if ((await checkConnectPMS(lf_id)) != 1) {
      throw ErrorHandler.badRequestError("Not connected to PMS");
    }
    const axiosInstance = axios.create({
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });
    let activePiecesWebhookUrl = process.env.MIDDLEWARE_CASE_LIST;
    let result = await axiosInstance.post(activePiecesWebhookUrl, {
      name: name,
      surn: surn,
      filter: filter,
      page: page,
      keyword: keyword,
      lf_id: lf_id,
    });
    console.log(process.env.MIDDLEWARE_CASE_LIST);
    console.log({
      name: name,
      surn: surn,
      filter: filter,
      page: page,
      keyword: keyword,
      lf_id: lf_id,
    });

    if (
      !result.data.data ||
      Object.keys(result.data.data).length == 0 ||
      result.data.data == null ||
      result.data.data == undefined
    ) {
      return [];
    }

    const convert_data = result.data.data.map(
      (item) => `${item.CODE} - ${item.MTDESC} - ${item.NAME + " " + item.SURN}`
    );
    return convert_data;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  }
};

export const getPrefixFromCaseId = async (case_id) => {
  let con = await pool.getConnection();
  try {
    let lf_id = await runQuery(con, "SELECT lf_id FROM cases WHERE id = ?", [
      case_id,
    ]);

    let result = await runQuery(
      con,
      "SELECT prefix FROM law_firm WHERE lf_id = ?",
      [lf_id[0].lf_id]
    );
    return result[0]?.prefix;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const exportDataToPMS1 = async (obj) => {
  const user = await userService.findUserById(obj.assigned_by);
  const output = { group: [] };

  for (let groupIndex = 0; groupIndex < obj.groups.length; groupIndex++) {
    const group = obj.groups[groupIndex];
    const questionData = [];
    for (
      let questionIndex = 0;
      questionIndex < group.questions.length;
      questionIndex++
    ) {
      const question = group.questions[questionIndex];
      const questionObj = {
        question: {
          text: (questionIndex + 1).toString(),
          ans_type: question.answer_type,
          answer: null,
        },
      };

      const answerEntry = obj.answer.find((ans) => ans.ques_id === question.id);
      if (answerEntry) {
        const answerType = parseInt(question.answer_type, 10);
        const answerData = answerEntry.answer;

        if ([6, 7, 8].includes(answerType)) {
          if (Array.isArray(answerData)) {
            const answers = [];
            for (const ansId of answerData) {
              const answerText = await findAnswerTextById(ansId);
              answers.push(answerText);
            }
            questionObj.question.answer = answers;
          } else {
            questionObj.question.answer = await findAnswerTextById(answerData);
          }
        } else if (answerType === 10) {
          questionObj.question.answer = await findAnswerModal(
            question.answers.id
          );
        } else {
          questionObj.question.answer = answerData;
        }
      }

      questionData.push(questionObj);
    }

    output.group.push({
      group_name: (groupIndex + 1).toString(),
      questions: questionData,
    });
  }
  return {
    output,
    assigned_by: {
      user_id: user[0]?.user_id,
      email: user[0]?.email,
      first_name: user[0]?.first_name,
      middle_name: user[0]?.middle_name,
      last_name: user[0]?.last_name,
    },
  };
};

export const exportDataToPMS = async (obj) => {
  const output = { group: [] };

  for (const group of obj.groups) {
    const questionData = [];
    for (const question of group.questions) {
      const questionObj = {
        question: {
          text: question.description,
          ans_type: question.answer_type,
          answer: null,
        },
      };

      const answerEntry = obj.answer.find((ans) => ans.ques_id === question.id);
      if (answerEntry) {
        const answerType = parseInt(question.answer_type, 10);
        const answerData = answerEntry.answer;

        if ([6, 7, 8].includes(answerType)) {
          if (Array.isArray(answerData)) {
            const answers = [];
            for (const ansId of answerData) {
              const answerText = await findAnswerTextById(ansId);
              answers.push(answerText);
            }
            questionObj.question.answer = answers;
          } else {
            questionObj.question.answer = await findAnswerTextById(answerData);
          }
        } else if (answerType === 10) {
          questionObj.question.answer = await findAnswerModal(
            question.answers.id
          );
        } else {
          questionObj.question.answer = answerData;
        }
      }

      questionData.push(questionObj);
    }

    output.group.push({ group_name: group.name, questions: questionData });
  }
  return output;
};

const reminder = schedule.scheduleJob("0 0 0 1 * *", async () => {
  let con = await pool.getConnection();
  try {
    let check_remind_time = await runQuery(
      con,
      "SELECT * FROM template_email WHERE mail_type = 3"
    );
    for (const check of check_remind_time) {
      if (!check.remind_time) continue;
      let check_open = await runQuery(
        con,
        "SELECT * FROM cases c WHERE status = 4 AND lf_id = ? AND DATEDIFF(NOW(), created_at) > ?",
        [check.lf_id, check.remind_time ?? 2]
      );
      for (const item of check_open) {
        let userInfo = await smsService.getInfo(item.id);

        let result = smsService.sendChaserMail(
          userInfo.first_name,
          userInfo.email,
          userInfo.phone.slice(-4),
          check_open.case_id,
          userInfo.lf_id,
          userInfo.user_id
        );
      }
    }
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
});

export const createCaseFromPMS = async (
  case_name,
  desc,
  qtn_id,
  pms_id,
  assigned_by,
  case_id_pms
) => {
  let con = await pool.getConnection();
  try {
    let get_user = await runQuery(
      con,
      "SELECT user_id FROM users WHERE pms_id = ?",
      [pms_id]
    );

    if (get_user.length == 0) {
      return 0;
    }

    let user_id = get_user[0]?.user_id;

    //grant permission client
    let user = await runQuery(
      con,
      "SELECT email, role FROM users WHERE user_id = ?",
      [assigned_by]
    );
    let check = await runQuery(
      con,
      "Select * from `cases` where user_id = ? and qtn_id = ?",
      [user_id, qtn_id]
    );
    if (check.length > 0) {
      if (check[0]?.status != 7) {
        return { case_id: check[0]?.id, check: 2 };
      } else {
        return { case_id: check[0]?.id, check: 0 };
      }
    }
    let obj = await getQuestionaire(qtn_id);
    if (Object.keys(obj).length == 0) {
      return 1;
    }
    if (obj?.status == 2 || obj?.status == 3) {
      return 2;
    }
    let case_id = await createCaseFromJson(
      obj,
      case_name,
      desc,
      user_id,
      qtn_id,
      assigned_by,
      case_id_pms
    );
    //grant permission client
    if (user[0].role != roles.LawfirmSuperAdmin) {
      await runQuery(
        con,
        "INSERT INTO grant_permission_case(case_id, email) VALUES (?, ?)",
        [case_id, user[0].email]
      );
    }
    return { case_id: case_id, check: 1 };
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getPartiesForAssignQtn = async (lf_id, case_id) => {
  let con = await pool.getConnection();
  try {
    if ((await checkConnectPMS(lf_id)) != 1) {
      throw ErrorHandler.badRequestError("Not connected to PMS");
    }

    const axiosInstance = axios.create({
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });
    let activePiecesWebhookUrl = process.env.MIDDLEWARE_GET_PARTIES_ASSIGN_QTN;
    let result = await axiosInstance.post(activePiecesWebhookUrl, {
      case_id: case_id,
      lf_id: lf_id,
    });

    for (const item of result.data) {
      item.phone = item.phone ? item.phone.replace(/[\s.]/g, "") : null;
      item.country_code = null;
      await runQuery(
        con,
        `INSERT INTO party_info(case_id, first_name, last_name, country_code, phone, email, lf_id)
        SELECT ?, ?, ?, ?, ?, ?, ?
        WHERE NOT EXISTS (
          SELECT 1 FROM party_info 
          WHERE case_id = ? 
            AND (first_name = ? OR (first_name IS NULL AND ? IS NULL))
            AND (last_name = ? OR (last_name IS NULL AND ? IS NULL))
        );`,
        [
          case_id,
          item.first_name,
          item.last_name,
          item.country_code,
          item.phone,
          item.email,
          item.lf_id,
          case_id,
          item.first_name,
          item.first_name,
          item.last_name,
          item.last_name,
          item.country_code,
          item.country_code,
          item.phone,
          item.phone,
          item.email,
          item.email,
          item.lf_id,
          item.lf_id,
        ]
      );
    }
    await con.commit();
    return result.data;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateEmailorPhoneParty = async (
  email,
  phone,
  first_name,
  last_name,
  case_id,
  lf_id,
  country_code
) => {
  let con = await pool.getConnection();
  try {
    country_code = getCountryCode(country_code);
    const check = await runQuery(
      con,
      "SELECT COUNT(*) FROM party_info WHERE lf_id = ? AND case_id = ? AND first_name = ? AND last_name = ?",
      [lf_id, case_id, first_name, last_name]
    );
    if (check[0]["COUNT(*)"] == 0) {
      throw ErrorHandler.badRequestError("Party not found");
    }
    await runQuery(
      con,
      "UPDATE party_info SET email = ?, phone = ?, country_code = ? WHERE lf_id = ? AND case_id = ? AND first_name = ? AND last_name = ?",
      [email, phone, country_code, lf_id, case_id, first_name, last_name]
    );
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const assignQtnToParty = async (
  case_name,
  desc,
  obj,
  qtn_id,
  assigned_by,
  case_id_pms,
  first_name,
  last_name,
  token_
) => {
  for (let key in token_) {
    obj = findAndReplace(obj, key, token_[key]);
  }
  let con = await pool.getConnection();
  try {
    let user_pms = await runQuery(
      con,
      "SELECT * FROM party_info WHERE first_name = ? AND last_name = ? AND case_id = ?",
      [first_name, last_name, case_id_pms]
    );
    let check = await runQuery(
      con,
      "SELECT * FROM cases WHERE qtn_id = ? AND party_info_id = ?",
      [qtn_id, user_pms[0]?.id]
    );
    if (check.length > 0) {
      if (check[0]?.status != 7) {
        return { case_id: check[0]?.id, check: 2 };
      } else {
        return { case_id: check[0]?.id, check: 0 };
      }
    }
    if (Object.keys(obj).length == 0) {
      throw ErrorHandler.badRequestError("Questionaire not found");
    }
    if (obj?.status == 2 || obj?.status == 3) {
      throw ErrorHandler.badRequestError("Questionaire is not active");
    }
    let case_id = await createCaseFromJsonForPMS(
      obj,
      case_name,
      desc,
      qtn_id,
      assigned_by,
      case_id_pms,
      first_name,
      last_name
    );

    return { case_id: case_id, check: 1 };
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

/**
 * Get case information for SMS/email notifications
 */
export const getInfo = async (case_id) => {
  let con = await pool.getConnection();
  try {
    const result = await runQuery(
      con,
      `SELECT c.id, c.case_name, c.user_id, u.email,
              ui.first_name, ui.last_name, ui.mb_phone as phone
       FROM cases c
       JOIN users u ON c.user_id = u.user_id
       JOIN user_info ui ON u.user_id = ui.user_id
       WHERE c.id = ?`,
      [case_id]
    );

    if (result.length === 0) {
      throw ErrorHandler.badRequestError("Case not found");
    }

    return result[0];
  } catch (error) {
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

/**
 * Get case data for export
 */
export const getCaseData = async (case_id) => {
  let con = await pool.getConnection();
  try {
    // Get basic case information
    const caseInfo = await runQuery(
      con,
      `SELECT c.*, u.email, ui.first_name, ui.last_name, lf.lf_org_name
       FROM cases c
       JOIN users u ON c.user_id = u.user_id
       JOIN user_info ui ON u.user_id = ui.user_id
       JOIN law_firm lf ON c.lf_id = lf.lf_id
       WHERE c.id = ?`,
      [case_id]
    );

    if (caseInfo.length === 0) {
      throw ErrorHandler.badRequestError("Case not found");
    }

    // Get case answers
    const answers = await runQuery(
      con,
      "SELECT * FROM answered WHERE case_id = ?",
      [case_id]
    );

    // Parse JSON answers
    const parsedAnswers = answers.map(ans => ({
      ...ans,
      answer: ans.parse == 1 ? JSON.parse(ans.answer) : ans.answer
    }));

    return {
      ...caseInfo[0],
      answers: parsedAnswers
    };
  } catch (error) {
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

/**
 * Get cases by law firm with pagination and filters
 */
export const getCasesByLawFirm = async (lf_id, page = 1, size = 10, status = null, user_id = null) => {
  let con = await pool.getConnection();
  try {
    let whereClause = "WHERE c.lf_id = ?";
    let params = [lf_id];

    if (status) {
      whereClause += " AND c.status = ?";
      params.push(status);
    }

    if (user_id) {
      whereClause += " AND c.user_id = ?";
      params.push(user_id);
    }

    const sql = `
      SELECT c.id, c.case_name, c.description, c.status, c.created_at, c.updated_at,
             u.email, ui.first_name, ui.last_name,
             s.name as status_name
      FROM cases c
      JOIN users u ON c.user_id = u.user_id
      JOIN user_info ui ON u.user_id = ui.user_id
      LEFT JOIN status s ON c.status = s.id
      ${whereClause}
      ORDER BY c.created_at DESC
      LIMIT ?, ?
    `;

    params.push((page - 1) * size, size);

    const cases = await runQuery(con, sql, params);

    // Get total count
    const countSql = `
      SELECT COUNT(*) as total
      FROM cases c
      ${whereClause}
    `;

    const countResult = await runQuery(con, countSql, params.slice(0, -2));

    return {
      cases,
      total: countResult[0].total,
      page,
      size,
      totalPages: Math.ceil(countResult[0].total / size)
    };
  } catch (error) {
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

/**
 * Export case to CSV format
 */
export const exportCaseToCSV = async (case_id) => {
  try {
    const caseData = await getCaseData(case_id);

    // Create CSV headers
    let csv = "Field,Value\n";

    // Add basic case information
    csv += `Case ID,${caseData.id}\n`;
    csv += `Case Name,${caseData.case_name || ''}\n`;
    csv += `Description,${caseData.desc || ''}\n`;
    csv += `Status,${caseData.status}\n`;
    csv += `Client Name,${caseData.first_name} ${caseData.last_name}\n`;
    csv += `Client Email,${caseData.email}\n`;
    csv += `Law Firm,${caseData.lf_org_name}\n`;
    csv += `Created At,${caseData.created_at}\n`;
    csv += `Updated At,${caseData.updated_at}\n`;

    // Add answers
    if (caseData.answers && caseData.answers.length > 0) {
      csv += "\nAnswers\n";
      csv += "Question ID,Answer\n";

      caseData.answers.forEach(answer => {
        const answerValue = typeof answer.answer === 'object'
          ? JSON.stringify(answer.answer).replace(/"/g, '""')
          : String(answer.answer || '').replace(/"/g, '""');
        csv += `${answer.question_id},"${answerValue}"\n`;
      });
    }

    return csv;
  } catch (error) {
    throw ErrorHandler.badRequestError(error.message);
  }
};

export const createCaseFromJsonForPMS = async (
  obj,
  case_name,
  desc,
  qtn_id,
  assigned_by,
  case_id_pms,
  first_name,
  last_name
) => {
  let retryCount = 0;
  while (retryCount < 5) {
    let con = await pool.getConnection();
    try {
      await createLink(obj);
      let id_list = [];
      var sql = fs.readFileSync("app/sql/createCaseWithId.sql").toString();
      var case_id = generateId();
      id_list[obj.id] = case_id;
      obj.id = case_id;

      let user_pms = await runQuery(
        con,
        "SELECT * FROM party_info WHERE first_name = ? AND last_name = ? AND case_id = ?",
        [first_name, last_name, case_id_pms]
      );

      let activePiecesWebhookUrl = process.env.MIDDELWARE_CASE_DATA;
      let result = await axios.post(activePiecesWebhookUrl, {
        case_id: case_id_pms,
        lf_id: obj.lf_id,
      });
      for (let key in result.data.data) {
        for (let value in result.data.data[key]) {
          findAndReplace(
            obj,
            `{{${key}.${value}}}`,
            result.data.data[key][value]
          );
        }
      }

      await con.beginTransaction();
      await runQuery(con, sql, [
        case_id,
        case_name,
        desc,
        4,
        assigned_by,
        null,
        obj.price,
        qtn_id,
        obj.lf_id,
        case_id_pms,
        user_pms[0]?.id,
        source.External,
      ]);
      for (const group of obj?.groups) {
        var groupId = generateId();
        id_list[group.id] = groupId;
        group.id = groupId;
        var sql = fs.readFileSync("app/sql/createGroup.sql").toString();
        await runQuery(con, sql, [
          groupId,
          group.name,
          null,
          case_id,
          group.tooltips,
        ]);
        for (const question of group?.questions) {
          var questionId = generateId();
          var ansId = generateId();
          id_list[question.id] = questionId;
          id_list[question.answers.id] = ansId;
          question.id = questionId;
          question.answers.id = ansId;
          var sql = fs.readFileSync("app/sql/createQuestion.sql").toString();
          await runQuery(con, sql, [
            questionId,
            question.name,
            question.description,
            question.answer_type,
            null,
            null,
            question.required,
            question.selectAnswerTable,
            question.tooltips,
            JSON.stringify(question.files) ?? "",
          ]);
          await runQuery(
            con,
            "INSERT INTO `answer` (`ans_id`, `name`, `ans_type`, `ques_id`,`modal_id`,`modal_type`, `expandable`) VALUES ( ?, ?, ?, ?, ?, ?, ?)",
            [
              ansId,
              question.answers.name,
              question.answer_type,
              null,
              question.answers.modal_id,
              question.answers.modal_type,
              question.answers.expandable,
            ]
          );
          if (question?.answers.content != null)
            if (typeof question?.answers.content == "string") {
              await runQuery(
                con,
                "INSERT INTO `answered` (`ques_id`, `case_id`, `answer`) VALUES (?, ?, ?)",
                [questionId, case_id, question?.answers.content]
              );
            } else {
              for (const answer of question?.answers.content) {
                var answerId = generateId();
                id_list[answer.id] = answerId;
                answer.id = answerId;
                var sql = fs
                  .readFileSync("app/sql/createAnswer.sql")
                  .toString();
                await runQuery(con, sql, [
                  answerId,
                  answer.name,
                  answer.description,
                  null,
                ]);
              }
            }
        }
      }
      for (const group of obj?.groups) {
        group?.linkedTo?.forEach((item, index) => {
          group.linkedTo[index] = id_list[item];
        });
        await runQuery(
          con,
          "Select * from `groups` where id = ? for update; update `groups` set answer_id = ? where id = ?",
          [
            group.id,
            typeof group?.linkedTo?.join(", ") !== "undefined"
              ? group?.linkedTo?.join(", ")
              : null,
            group.id,
          ]
        );
        for (const question of group?.questions) {
          question?.linkedTo?.forEach((item, index) => {
            question.linkedTo[index] = id_list[item];
          });
          await runQuery(
            con,
            "select * from `questions` where id =? for update;update `questions` set parent_answer = ?,gr_id = ? where id = ?",
            [
              question.id,
              typeof question?.linkedTo?.join(", ") !== "undefined"
                ? question?.linkedTo?.join(", ")
                : null,
              group.id,
              question.id,
            ]
          );
          await runQuery(
            con,
            "select * from `answer` where ans_id =? for update; update `answer` set ques_id = ? where ans_id = ?",
            [question.answers.id, question.id, question.answers.id]
          );
          if (question?.answers.content != null)
            if (typeof question?.answers.content != "string") {
              for (const answers of question?.answers.content) {
                await runQuery(
                  con,
                  "select * from `answers` where id =? for update; update `answers` set ques_id = ? where id = ?",
                  [answers.id, question.answers.id, answers.id]
                );
              }
            }
        }
      }
      for (let conditions of obj?.conditions) {
        let new_con = replaceQuestionIds(conditions, id_list);
        conditions.linkTo = id_list[conditions.linkTo];
        if (conditions?.repeatedBy)
          conditions.repeatedBy = id_list[conditions.repeatedBy];
        await runQuery(
          con,
          "INSERT INTO `conditions`(template_id,content) VALUES (?,?)",
          [case_id, JSON.stringify(new_con)]
        );
      }
      await con.commit();
      return case_id;
    } catch (error) {
      if (error.code === "ER_LOCK_DEADLOCK") {
        retryCount++;
        await con.rollback();
        await new Promise((r) => setTimeout(r, 200));
      } else {
        await con.rollback();
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    } finally {
      con.destroy();
    }
  }
};

export const checkInfoParty = async (first_name, last_name, case_id, lf_id) => {
  let con = await pool.getConnection();
  try {
    let result = await runQuery(
      con,
      "SELECT * FROM party_info WHERE first_name = ? AND last_name = ? AND case_id = ? AND lf_id = ?",
      [first_name, last_name, case_id, lf_id]
    );
    return result;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const uploadDocument = async (file) => {
  try {
    const { sync } = new S3SyncClient({ client: s3Client });
    const filename = `${Date.now()}-${file.originalname}`;
    let path = `static/file/${filename}`;
    fs.writeFileSync(path, file.buffer);
    await sync("static", process.env.S3_PATH);
    return filename;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  }
};

export const downloadDocument = async (file) => {
  const getContentType = (filename) => {
    const ext = filename.split(".").pop().toLowerCase();
    const contentTypes = {
      pdf: "application/pdf",
      doc: "application/msword",
      docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      xls: "application/vnd.ms-excel",
      xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      png: "image/png",
      jpg: "image/jpeg",
      jpeg: "image/jpeg",
      gif: "image/gif",
    };
    return contentTypes[ext] || "application/octet-stream";
  };

  const command = new GetObjectCommand({
    Bucket: process.env.S3_BUCKET_NAME,
    Key: "file/" + file,
    ResponseContentType: getContentType(file),
    ResponseContentDisposition: `attachment; filename="${file
      .split("/")
      .pop()}"`,
    ResponseCacheControl: "no-cache, no-store, must-revalidate, max-age=0",
  });

  const signedUrl = await getSignedUrl(s3Client, command, {
    expiresIn: 60,
  });

  return signedUrl;
};

export const extractFileNames = (obj) => {
  let fileNames = [];
  if (Array.isArray(obj)) {
    obj.forEach((item) => {
      fileNames = fileNames.concat(extractFileNames(item));
    });
  } else if (typeof obj === "object" && obj !== null) {
    if (obj.name && obj.path) {
      fileNames.push(obj.path);
    }
    for (let key in obj) {
      if (typeof obj[key] === "object" && obj[key] !== null) {
        fileNames = fileNames.concat(extractFileNames(obj[key]));
      }
    }
  }

  return fileNames;
};

export const updateStatusExported = async (case_id) => {
  let con = await pool.getConnection();
  try {
    await runQuery(
      con,
      "UPDATE cases SET status = 8, update_at = NOW() WHERE id = ?",
      [case_id]
    );
    return;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

// Helper function to find group by name
const findGroupByName = (groups, groupName) => {
  return groups.find(group => group.name === groupName);
};

// Helper function to find question by index in a group
const findQuestionByIndex = (group, questionIndex) => {
  return group?.questions?.[questionIndex];
};

export const generateSpreadsheet = async (case_id) => {
  const case_ = await getCase(case_id);
  const answerMap = new Map(
    case_.answer.map((ans) => [ans.ques_id, ans.answer])
  );
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.readFile("app/Template.xlsx");
  const worksheet = workbook.getWorksheet("Summary");

  // Map values to the spreadsheet

  //Case ref
  // worksheet.getCell("C2").value = case_.name || "";
  worksheet.getCell("C2").value = "";
  worksheet.getCell("C3").value = case_.created_at
    ? format(new Date(case_.created_at), "dd/MM/yyyy")
    : "";
  worksheet.getCell("C4").value = case_.case_id_pms || "";

  //Customer detail
  const aboutYouGroup = findGroupByName(case_.groups, "About you");
  const customerDetailQuestion = findQuestionByIndex(aboutYouGroup, 1);
  const customer_detail_data = await findAnswerModal(
    customerDetailQuestion?.answers?.id
  );

  worksheet.getCell("C6").value =
    (customer_detail_data[0]["Title"] ?? "") +
    (customer_detail_data[0]["Middle name(s)"]
      ? " " + customer_detail_data[0]["Middle name(s)"] + " "
      : " ") +
    (customer_detail_data[0]["First name"] + " " ?? " ") +
    (customer_detail_data[0]["Surname"] ?? "");
  worksheet.getCell("C7").value =
    customer_detail_data[0]["Your relationship to the Deceased"] || "";
  // worksheet.getCell("C8").value =
  //   (await findAnswerTextById(
  //     answerMap.get(case_.groups[2].questions[0].id)
  //   )) == "Yes"
  //     ? "Executor"
  //     : "Administrator";
  worksheet.getCell("C10").value =
    customer_detail_data[0]["Home phone number"] || "";
  worksheet.getCell("C11").value =
    customer_detail_data[0]["Work phone number"] || "";
  worksheet.getCell("C12").value =
    customer_detail_data[0]["Mobile phone number"] || "";
  worksheet.getCell("C14").value = customer_detail_data[0]["Date of birth"]
    ? format(new Date(customer_detail_data[0]["Date of birth"]), "dd/MM/yyyy")
    : "";
  worksheet.getCell("C15").value =
    customer_detail_data[0]["Email address"] || "";
  worksheet.getCell("C16").value =
    customer_detail_data[0]["Address line 1"] || "";
  worksheet.getCell("C17").value = `${customer_detail_data[0]["Town"] ?? ""} ${
    customer_detail_data[0]["County"] ?? ""
  }`;
  worksheet.getCell("C18").value = customer_detail_data[0]["Postcode"] || "";

  //Deceased detail
  const deceasedGroup = findGroupByName(case_.groups, "The Deceased");
  const deceasedDetailQuestion = findQuestionByIndex(deceasedGroup, 0);
  const deceased_detail_data = await findAnswerModal(
    deceasedDetailQuestion?.answers?.id
  );

  // Build deceased full name with proper spacing
  const deceasedNameParts = [
    deceased_detail_data[0]["Title"],
    deceased_detail_data[0]["First name"],
    deceased_detail_data[0]["Middle name(s)"],
    deceased_detail_data[0]["Surname"]
  ].filter(part => part && part.trim() !== ""); // Remove empty/undefined parts

  worksheet.getCell("C20").value = deceasedNameParts.join(" ");
  worksheet.getCell("C21").value =
    answerMap.get(findQuestionByIndex(deceasedGroup, 4)?.id) || "No";

  worksheet.getCell("C22").value =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(deceasedGroup, 2)?.id)
    )) || "";
  worksheet.getCell("C23").value =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(deceasedGroup, 5)?.id)
    )) || "";
  worksheet.getCell("C24").value = deceased_detail_data[0]["Date of death"]
    ? format(new Date(deceased_detail_data[0]["Date of death"]), "dd/MM/yyyy")
    : "";
  worksheet.getCell("C25").value = deceased_detail_data[0]["Date of birth"]
    ? format(new Date(deceased_detail_data[0]["Date of birth"]), "dd/MM/yyyy")
    : "";
  worksheet.getCell("C26").value =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(deceasedGroup, 7)?.id)
    )) || "";
  worksheet.getCell("C30").value = `${
    deceased_detail_data[0]["Address line 1"] || ""
  } ${deceased_detail_data[0]["Address line 2"] || ""}${
    deceased_detail_data[0]["Address line 3"] || ""
  }`;
  worksheet.getCell("C31").value = `${deceased_detail_data[0]["Town"] || ""} ${
    deceased_detail_data[0]["County"] || ""
  }`;
  worksheet.getCell("C32").value = deceased_detail_data[0]["Postcode"] || "";
  worksheet.getCell("C34").value =
    deceased_detail_data[0]["National Insurance number"] || "";

  // Wills
  const willGroup = findGroupByName(case_.groups, "Will");
  worksheet.getCell("C37").value =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(deceasedGroup, 1)?.id)
    )) || "";
  worksheet.getCell("C38").value =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(willGroup, 0)?.id)
    )) || "";
  worksheet.getCell("C39").value =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(willGroup, 3)?.id)
    )) || "";
  worksheet.getCell("C40").value =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(willGroup, 4)?.id)
    )) || "";
  const executorQuestion = findQuestionByIndex(willGroup, 5);
  const excutor = await findAnswerModal(
    executorQuestion?.answers?.id
  );
  worksheet.getCell("C41").value = excutor.length || "";
  if (excutor && excutor.length > 0) {
    const executorsSheet = workbook.addWorksheet("Executors", {
      properties: { defaultColWidth: 30 },
    });
    const headers = [
      "Executor Name",
      "Address - House number/name & street",
      "City & County",
      "Postcode",
      "Date of Birth",
    ];

    executorsSheet.addRow(headers);
    executorsSheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
    });

    const fillExecutorData = (executor, rowIndex) => {
      executorsSheet.getCell(`A${rowIndex}`).value = `${
        executor["Title"] ?? ""
      } ${executor["First name"] ?? ""} ${executor["Surname"] ?? ""} ${
        executor["Middle name(s)"] ?? ""
      }`;
      executorsSheet.getCell(`B${rowIndex}`).value = `${
        executor["Address line 1"] ?? ""
      } ${executor["Address line 2"] ?? ""} ${
        executor["Address line 3"] ?? ""
      }`;
      executorsSheet.getCell(`C${rowIndex}`).value =
        executor["Town"] || "" + executor["County"] || "";
      executorsSheet.getCell(`D${rowIndex}`).value = executor["Postcode"] || "";
      executorsSheet.getCell(`E${rowIndex}`).value = executor["Date of birth"]
        ? format(new Date(executor["Date of birth"]), "dd/MM/yyyy")
        : "";
    };

    let rowIndex = 2;
    for (let i = 0; i < excutor.length; i++) {
      fillExecutorData(excutor[i], rowIndex);
      rowIndex++;
    }
  }

  // TODO: Intestacy
  const check_intestacy = worksheet.getCell("C37").value;
  if (check_intestacy === "No") {
    const intestacyGroup = findGroupByName(case_.groups, "Intestacy");
    const intestacyQuestion = findQuestionByIndex(intestacyGroup, 0);
    const intestacy_data = await findAnswerModal(
      intestacyQuestion?.answers?.id
    );
    if (intestacy_data) {
      // Build intestacy name with proper spacing
      const intestacyNameParts = [
        intestacy_data["First name"],
        intestacy_data["Middle name(s)"],
        intestacy_data["Surname"]
      ].filter(part => part && part.trim() !== "");

      worksheet.getCell("C45").value = intestacyNameParts.join(" ");
    }
    if (excutor && excutor.length > 0) {
      worksheet.getCell("C46").value =
        (excutor[0]["Title"] ?? "") +
        " " +
        (excutor[0]["First name"] ?? "") +
        " " +
        (excutor[0]["Surname"] ?? "") +
        " " +
        (excutor[0]["Middle name(s)"] ?? "");
    } else {
      worksheet.getCell("C46").value = "";
    }
    worksheet.getCell("C48").value = await findAnswerTextById(
      answerMap.get(findQuestionByIndex(intestacyGroup, 2)?.id)
    );
  }
  const property_check = await findAnswerTextById(
    answerMap.get(findQuestionByIndex(deceasedGroup, 8)?.id)
  );
  worksheet.getCell("C51").value = property_check ?? "";
  worksheet.getCell("E4").value = property_check ?? "";

  // Property
  const propertyGroup = findGroupByName(case_.groups, "Property");
  const propertyQuestion = findQuestionByIndex(propertyGroup, 0);
  const property_data = await findAnswerModal(
    propertyQuestion?.answers?.id
  );
  if (property_check === "Yes") {
    const propertySheet = workbook.addWorksheet("Properties", {
      properties: { defaultColWidth: 30 },
    });
    const headers = [
      "Ownership",
      "If owned, how was the property held?",
      "Total value of this property",
      "If jointly owned, value of the Deceased's share.",
      "Is the property to be sold or transferred to someone else?",
      "Is there a mortgage on this property?",
      "If there is a mortgage, what is the amount outstanding?",
    ];

    propertySheet.addRow(headers);
    propertySheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
    });

    const fillPropertyData = (property, rowIndex) => {
      propertySheet.getCell(`A${rowIndex}`).value = property["Ownership"] || "";
      propertySheet.getCell(`B${rowIndex}`).value =
        property["If owned, how was the property held?"] || "";
      propertySheet.getCell(`C${rowIndex}`).value =
        "£" +
        parseFloat(
          property["Total value of this property"] || 0
        ).toLocaleString("en-GB", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        });
      propertySheet.getCell(`D${rowIndex}`).value =
        "£" +
          parseFloat(
            property["If jointly owned, value of the Deceased's share."] || 0
          ).toLocaleString("en-GB", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || "£0.0";
      propertySheet.getCell(`E${rowIndex}`).value =
        property[
          "Is the property to be sold or transferred to someone else?"
        ] || "";
      propertySheet.getCell(`F${rowIndex}`).value =
        property["Is there a mortgage on this property?"] || "";
      propertySheet.getCell(`G${rowIndex}`).value =
        "£" +
          parseFloat(
            property[
              "If there is a mortgage, what is the amount outstanding?"
            ] || 0
          ).toLocaleString("en-GB", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || "£0.0";
    };

    let rowIndex = 2;
    var total_amount = 0;
    var total_owned_property_own = 0;
    for (let i = 0; i < property_data.length; i++) {
      fillPropertyData(property_data[i], rowIndex);
      if (property_data[i]["Ownership"] !== "Rented") {
        if (
          property_data[i]["If owned, how was the property held?"] ==
          "Jointly as joint tenants"
        ) {
          total_amount +=
            parseFloat(
              property_data[i][
                "If jointly owned, value of the Deceased's share."
              ]
            ) || 0;
        } else {
          total_amount +=
            parseFloat(property_data[i]["Total value of this property"]) || 0;
        }

        // total_amount +=
        //   parseFloat(
        //     property_data[i][
        //       "If there is a mortgage, what is the amount outstanding?"
        //     ]
        //   ) || 0;
      }
      rowIndex++;
    }
  }

  // # Assests

  // ## Bank accounts
  const bank_check = await findAnswerTextById(
    answerMap.get(findQuestionByIndex(deceasedGroup, 9)?.id)
  );
  worksheet.getCell("E7").value = bank_check || "";

  if (bank_check === "Yes") {
    const bankGroup = findGroupByName(case_.groups, "Bank and Building Society accounts");
    const bankQuestion = findQuestionByIndex(bankGroup, 0);
    const bank_data = await findAnswerModal(
      bankQuestion?.answers?.id
    );

    const bankSheet = workbook.addWorksheet("Bank Accounts", {
      properties: { defaultColWidth: 30 },
    });
    const headers = [
      "Bank or building society name",
      "Account number",
      "Balance at date of death",
    ];
    bankSheet.addRow(headers);
    bankSheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
    });

    const fillBankData = (bank, rowIndex) => {
      bankSheet.getCell(`A${rowIndex}`).value =
        bank["Bank or building society name"] || "";
      bankSheet.getCell(`B${rowIndex}`).value = bank["Account number"] || "";
      bankSheet.getCell(`C${rowIndex}`).value =
        "£" +
          parseFloat(bank["Balance at date of death"] || 0).toLocaleString(
            "en-GB",
            {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }
          ) || "£0.0";
    };

    let rowIndex = 2;
    var total_bank = 0;
    for (let i = 0; i < bank_data.length; i++) {
      fillBankData(bank_data[i], rowIndex);
      total_bank += parseFloat(bank_data[i]["Balance at date of death"]);
      rowIndex++;
    }
  }

  // ## Stock & Shares
  const stock_check = await findAnswerTextById(
    answerMap.get(findQuestionByIndex(deceasedGroup, 11)?.id)
  );
  worksheet.getCell("E10").value = stock_check || "";

  if (stock_check === "Yes") {
    const stockGroup = findGroupByName(case_.groups, "Stocks and Shares");
    const stockQuestion = findQuestionByIndex(stockGroup, 0);
    const stock_data = await findAnswerModal(
      stockQuestion?.answers?.id
    );

    const stockSheet = workbook.addWorksheet("Stock & Shares", {
      properties: { defaultColWidth: 30 },
    });
    const headers = [
      "Name of holding",
      "Type of holding",
      "Value at date of death",
    ];
    stockSheet.addRow(headers);
    stockSheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
    });

    const fillStockData = (stock, rowIndex) => {
      stockSheet.getCell(`A${rowIndex}`).value = stock["Name of holding"] || "";
      stockSheet.getCell(`B${rowIndex}`).value = stock["Type of holding"] || "";
      stockSheet.getCell(`C${rowIndex}`).value =
        "£" +
          parseFloat(stock["Value at date of death"] || 0).toLocaleString(
            "en-GB",
            {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }
          ) || "£0.0";
    };

    let rowIndex = 2;
    var total_stock = 0;
    for (let i = 0; i < stock_data.length; i++) {
      fillStockData(stock_data[i], rowIndex);
      total_stock += parseFloat(stock_data[i]["Value at date of death"]);
      rowIndex++;
    }
  }

  // ## Investments
  const investment_check = await findAnswerTextById(
    answerMap.get(findQuestionByIndex(deceasedGroup, 12)?.id)
  );
  worksheet.getCell("E13").value = investment_check || "";

  if (investment_check === "Yes") {
    const investmentGroup = findGroupByName(case_.groups, "Investments");
    const investmentQuestion = findQuestionByIndex(investmentGroup, 0);
    const investment_data = await findAnswerModal(
      investmentQuestion?.answers?.id
    );

    const investmentSheet = workbook.addWorksheet("Investments", {
      properties: { defaultColWidth: 30 },
    });
    const headers = [
      "Name of investment",
      "Type of investment",
      "Value at date of death",
    ];
    investmentSheet.addRow(headers);
    investmentSheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
    });

    const fillInvestmentData = (investment, rowIndex) => {
      investmentSheet.getCell(`A${rowIndex}`).value =
        investment["Name of investment"] || "";
      investmentSheet.getCell(`B${rowIndex}`).value =
        investment["Type of investment"] || "";
      investmentSheet.getCell(`C${rowIndex}`).value =
        "£" +
          parseFloat(investment["Value at date of death"] || 0).toLocaleString(
            "en-GB",
            {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }
          ) || "£0.0";
    };

    let rowIndex = 2;
    var total_investment = 0;
    for (let i = 0; i < investment_data.length; i++) {
      fillInvestmentData(investment_data[i], rowIndex);
      total_investment += parseFloat(
        investment_data[i]["Value at date of death"]
      );
      rowIndex++;
    }
  }

  // ## Pension
  const pensionGroup = findGroupByName(case_.groups, "Pensions");
  const pension_check = await findAnswerTextById(
    answerMap.get(findQuestionByIndex(pensionGroup, 1)?.id)
  );
  worksheet.getCell("E16").value = pension_check || "";
  worksheet.getCell("F17").value = pension_check || "";

  if (pension_check === "Yes") {
    const pensionQuestion = findQuestionByIndex(pensionGroup, 2);
    const pension_data = await findAnswerModal(
      pensionQuestion?.answers?.id
    );

    const pensionSheet = workbook.addWorksheet("Private Pension", {
      properties: { defaultColWidth: 30 },
    });
    const headers = [
      "Name of pension provider",
      "Pension plan number",
      "Value at date of death",
    ];
    pensionSheet.addRow(headers);
    pensionSheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
    });

    const fillPensionData = (pension, rowIndex) => {
      pensionSheet.getCell(`A${rowIndex}`).value =
        pension["Name of pension provider"] || "";
      pensionSheet.getCell(`B${rowIndex}`).value =
        pension["Pension plan number"] || "";
      pensionSheet.getCell(`C${rowIndex}`).value =
        "£" +
          parseFloat(pension["Value at date of death"] || 0).toLocaleString(
            "en-GB",
            {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }
          ) || "£0.0";
    };

    let rowIndex = 2;
    var total_pension = 0;
    for (let i = 0; i < pension_data.length; i++) {
      fillPensionData(pension_data[i], rowIndex);
      total_pension += parseFloat(pension_data[i]["Value at date of death"]);
      rowIndex++;
    }
  }

  // ## Life policies
  const life_policy_check = await findAnswerTextById(
    answerMap.get(findQuestionByIndex(deceasedGroup, 14)?.id)
  );
  worksheet.getCell("E20").value = life_policy_check || "";

  if (life_policy_check === "Yes") {
    const lifePolicyGroup = findGroupByName(case_.groups, "Life policies");
    const lifePolicyQuestion = findQuestionByIndex(lifePolicyGroup, 0);
    const life_policy_data = await findAnswerModal(
      lifePolicyQuestion?.answers?.id
    );

    const lifePolicySheet = workbook.addWorksheet("Life Policies", {
      properties: { defaultColWidth: 30 },
    });
    const headers = [
      "Name of insurance company",
      "Policy number",
      "Value at date of death",
    ];
    lifePolicySheet.addRow(headers);
    lifePolicySheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
    });

    const fillLifePolicyData = (lifePolicy, rowIndex) => {
      lifePolicySheet.getCell(`A${rowIndex}`).value =
        lifePolicy["Name of insurance company"] || "";
      lifePolicySheet.getCell(`B${rowIndex}`).value =
        lifePolicy["Policy number"] || "";
      lifePolicySheet.getCell(`C${rowIndex}`).value =
        "£" +
          parseFloat(lifePolicy["Value at date of death"] || 0).toLocaleString(
            "en-GB",
            {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }
          ) || "£0.0";
    };

    let rowIndex = 2;
    var total_life_policy = 0;
    for (let i = 0; i < life_policy_data.length; i++) {
      fillLifePolicyData(life_policy_data[i], rowIndex);
      total_life_policy += parseFloat(
        life_policy_data[i]["Value at date of death"]
      );
      rowIndex++;
    }
  }

  // ## NS&I
  const nsandi_check = await findAnswerTextById(
    answerMap.get(findQuestionByIndex(deceasedGroup, 10)?.id)
  );
  worksheet.getCell("E23").value = nsandi_check || "";

  if (nsandi_check === "Yes") {
    const nsandiGroup = findGroupByName(case_.groups, "NS&I");
    const nsandiQuestion = findQuestionByIndex(nsandiGroup, 0);
    const nsandi_data = await findAnswerModal(
      nsandiQuestion?.answers?.id
    );

    const nsandiSheet = workbook.addWorksheet("NS&I", {
      properties: { defaultColWidth: 30 },
    });
    const headers = [
      "Type of NS&I account",
      "Account number",
      "Value at date of death",
    ];
    nsandiSheet.addRow(headers);
    nsandiSheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
    });

    const fillNsandiData = (nsandi, rowIndex) => {
      nsandiSheet.getCell(`A${rowIndex}`).value =
        nsandi["Type of NS&I account"] || "";
      nsandiSheet.getCell(`B${rowIndex}`).value =
        nsandi["Account number"] || "";
      nsandiSheet.getCell(`C${rowIndex}`).value =
        "£" +
          parseFloat(nsandi["Value at date of death"] || 0).toLocaleString(
            "en-GB",
            {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }
          ) || "£0.0";
    };

    let rowIndex = 2;
    var total_ns_and_i = 0;
    for (let i = 0; i < nsandi_data.length; i++) {
      fillNsandiData(nsandi_data[i], rowIndex);
      total_ns_and_i += parseFloat(nsandi_data[i]["Value at date of death"]);
      rowIndex++;
    }
  }

  // ## Other assets
  const otherAssetsGroup = findGroupByName(case_.groups, "Other assets");
  const other_assets_check = await findAnswerTextById(
    answerMap.get(findQuestionByIndex(otherAssetsGroup, 12)?.id)
  );

  worksheet.getCell("E26").value = other_assets_check || "";

  if (other_assets_check === "Yes") {
    const otherAssetsQuestion = findQuestionByIndex(otherAssetsGroup, 13);
    const other_assets_data = await findAnswerModal(
      otherAssetsQuestion?.answers?.id
    );

    const otherAssetsSheet = workbook.addWorksheet("Other Assets", {
      properties: { defaultColWidth: 30 },
    });

    const headers = ["Description of asset", "Value at date of death"];
    otherAssetsSheet.addRow(headers);
    otherAssetsSheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
    });

    const fillOtherAssetsData = (otherAssets, rowIndex) => {
      otherAssetsSheet.getCell(`A${rowIndex}`).value =
        otherAssets["Description of asset"] || "";
      otherAssetsSheet.getCell(`B${rowIndex}`).value =
        "£" +
          parseFloat(otherAssets["Value at date of death"] || 0).toLocaleString(
            "en-GB",
            {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }
          ) || "£0.0";
    };

    let rowIndex = 2;
    var total_other_assets = 0;
    for (let i = 0; i < other_assets_data.length; i++) {
      fillOtherAssetsData(other_assets_data[i], rowIndex);
      total_other_assets += parseFloat(
        other_assets_data[i]["Value at date of death"]
      );
      rowIndex++;
    }
  }

  const f4 = total_amount;
  const f7 = total_bank;
  const f10 = total_stock;
  const f13 = total_investment;
  const f16 = total_pension;
  const f20 = total_life_policy;
  const f23 = total_ns_and_i;
  const f26 = total_other_assets;

  worksheet.getCell("F4").value = f4;
  worksheet.getCell("F7").value = f7;
  worksheet.getCell("F10").value = f10;
  worksheet.getCell("F13").value = f13;
  worksheet.getCell("F16").value = f16;
  worksheet.getCell("F20").value = f20;
  worksheet.getCell("F23").value = f23;
  worksheet.getCell("F26").value = f26;

  // TODO: Total £
  const personal_loan_check = await findAnswerTextById(
    answerMap.get(findQuestionByIndex(deceasedGroup, 19)?.id)
  );
  if (personal_loan_check === "Yes") {
    const personalLoanGroup = findGroupByName(case_.groups, "Personal loans");
    const personalLoanQuestion = findQuestionByIndex(personalLoanGroup, 0);
    const personal_loan = await findAnswerModal(
      personalLoanQuestion?.answers?.id
    );

    const personal_loan_sheet = workbook.addWorksheet("Personal Loan", {
      properties: { defaultColWidth: 30 },
    });
    const headers = [
      "Name of creditor",
      "Account number",
      "Amount owing at date of death.",
    ];

    personal_loan_sheet.addRow(headers);
    personal_loan_sheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
    });

    const fillPersonalLoanData = (personalLoan, rowIndex) => {
      personal_loan_sheet.getCell(`A${rowIndex}`).value =
        personalLoan["Name of creditor"] || "";
      personal_loan_sheet.getCell(`B${rowIndex}`).value =
        personalLoan["Account number"] || "";
      personal_loan_sheet.getCell(`C${rowIndex}`).value =
        "£" +
          parseFloat(
            personalLoan["Amount owing at date of death."] || 0
          ).toLocaleString("en-GB", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || "£0.0";
    };

    let rowIndex = 2;
    var total_personal_loan = 0;
    for (let i = 0; i < personal_loan.length; i++) {
      fillPersonalLoanData(personal_loan[i], rowIndex);
      total_personal_loan += parseFloat(
        personal_loan[i]["Amount owing at date of death."]
      );
      rowIndex++;
    }
  }

  const credit_check = await findAnswerTextById(
    answerMap.get(findQuestionByIndex(deceasedGroup, 20)?.id)
  );
  if (credit_check === "Yes") {
    const creditGroup = findGroupByName(case_.groups, "Credit and store cards");
    const creditQuestion = findQuestionByIndex(creditGroup, 0);
    const credit_data = await findAnswerModal(
      creditQuestion?.answers?.id
    );
    const credit_sheet = workbook.addWorksheet("Credit Card", {
      properties: { defaultColWidth: 30 },
    });

    const headers = [
      "Name of credit or store card company",
      "Account number",
      "Amount owing at date of death",
    ];

    credit_sheet.addRow(headers);
    credit_sheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
    });

    const fillCreditData = (credit, rowIndex) => {
      credit_sheet.getCell(`A${rowIndex}`).value =
        credit["Name of credit or store card company"] || "";
      credit_sheet.getCell(`B${rowIndex}`).value =
        credit["Account number"] || "";
      credit_sheet.getCell(`C${rowIndex}`).value =
        "£" +
          parseFloat(
            credit["Amount owing at date of death"] || 0
          ).toLocaleString("en-GB", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || "£0.0";
    };

    let rowIndex = 2;
    var total_credit = 0;
    for (let i = 0; i < credit_data.length; i++) {
      fillCreditData(credit_data[i], rowIndex);
      total_credit += parseFloat(
        credit_data[i]["Amount owing at date of death"]
      );
      rowIndex++;
    }
  }

  const other_check = await findAnswerTextById(
    answerMap.get(findQuestionByIndex(deceasedGroup, 21)?.id)
  );

  if (other_check === "Yes") {
    const otherLiabilitiesGroup = findGroupByName(case_.groups, "Other liabilities");
    const otherLiabilitiesQuestion = findQuestionByIndex(otherLiabilitiesGroup, 0);
    const other_data = await findAnswerModal(
      otherLiabilitiesQuestion?.answers?.id
    );
    const other_sheet = workbook.addWorksheet("Other Liabilities", {
      properties: { defaultColWidth: 30 },
    });

    const headers = [
      "Name of creditor",
      "Description of liability",
      "Amount due at date of death",
    ];

    other_sheet.addRow(headers);
    other_sheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
    });

    const fillOtherData = (other, rowIndex) => {
      other_sheet.getCell(`A${rowIndex}`).value =
        other["Name of creditor"] || "";
      other_sheet.getCell(`B${rowIndex}`).value =
        other["Description of liability"] || "";
      other_sheet.getCell(`C${rowIndex}`).value =
        "£" +
          parseFloat(other["Amount due at date of death"] || 0).toLocaleString(
            "en-GB",
            { minimumFractionDigits: 2, maximumFractionDigits: 2 }
          ) || "£0.00";
    };

    let rowIndex = 2;
    var total_other = 0;
    for (let i = 0; i < other_data.length; i++) {
      fillOtherData(other_data[i], rowIndex);
      total_other += parseFloat(other_data[i]["Amount due at date of death"]);
      rowIndex++;
    }
  }

  const funeralGroup = findGroupByName(case_.groups, "Funeral");
  let i2 = answerMap.get(findQuestionByIndex(funeralGroup, 0)?.id) || 0.0;
  let i3 = answerMap.get(findQuestionByIndex(funeralGroup, 2)?.id) || 0.0;
  let i6 = total_personal_loan || 0.0;
  let i9 = total_credit || 0.0;
  let i12 = total_other || 0.0;

  worksheet.getCell("I2").value = parseFloat(i2) || 0.0;
  worksheet.getCell("I3").value = parseFloat(i3) || 0.0;
  worksheet.getCell("I6").value = parseFloat(i6) || 0.0;
  worksheet.getCell("I9").value = parseFloat(i9) || 0.0;
  worksheet.getCell("I12").value = parseFloat(i12) || 0.0;

  worksheet.getCell("I15").value =
    parseFloat(total_amount || 0) +
    parseFloat(i2 || 0) +
    parseFloat(i3 || 0) +
    parseFloat(i6 || 0) +
    parseFloat(i9 || 0) +
    parseFloat(i12 || 0);

  worksheet.getCell("I16").value =
    parseFloat(total_owned_property_own || 0) +
    parseFloat(f4 || 0) +
    parseFloat(f7 || 0) +
    parseFloat(f10 || 0) +
    parseFloat(f13 || 0) +
    parseFloat(f16 || 0) +
    parseFloat(f20 || 0) +
    parseFloat(f23 || 0) +
    parseFloat(f26 || 0);

  // TODO: Tax
  const giftsGroup = findGroupByName(case_.groups, "Gifts");

  const i23 =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(deceasedGroup, 17)?.id)
    )) || "";
  const i24 = answerMap.get(findQuestionByIndex(giftsGroup, 0)?.id) || "";
  const i27 =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(otherAssetsGroup, 0)?.id)
    )) || "";
  const i29 = answerMap.get(findQuestionByIndex(otherAssetsGroup, 1)?.id) || "";
  const i32 =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(otherAssetsGroup, 2)?.id)
    )) || "";
  const i34 = answerMap.get(findQuestionByIndex(otherAssetsGroup, 3)?.id) || "";
  const i37 =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(otherAssetsGroup, 4)?.id)
    )) || "";
  const i38 =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(otherAssetsGroup, 5)?.id)
    )) || "";
  const i42 =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(otherAssetsGroup, 6)?.id)
    )) || "";
  const i43 = answerMap.get(findQuestionByIndex(otherAssetsGroup, 7)?.id) || "";
  const i46 =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(otherAssetsGroup, 8)?.id)
    )) || "";
  const i47 = answerMap.get(findQuestionByIndex(otherAssetsGroup, 9)?.id) || "";
  const i50 =
    (await findAnswerTextById(
      answerMap.get(findQuestionByIndex(otherAssetsGroup, 10)?.id)
    )) || "";

  worksheet.getCell("I23").value = i23;
  worksheet.getCell("I24").value = i24;
  worksheet.getCell("I27").value = i27;
  worksheet.getCell("I29").value = i29;
  worksheet.getCell("I32").value = i32;
  worksheet.getCell("I34").value = i34;
  worksheet.getCell("I37").value = i37;
  worksheet.getCell("I38").value = i38;
  worksheet.getCell("I42").value = i42;
  worksheet.getCell("I43").value = i43;
  worksheet.getCell("I46").value = i46;
  worksheet.getCell("I47").value = i47;
  worksheet.getCell("I50").value = i50;

  const buffer = await workbook.xlsx.writeBuffer();
  // fs.writeFileSync(`case_${case_id}.xlsx`, buffer);
  return buffer;
};

export const generateSpreadsheet1 = async (case_id) => {
  const case_ = await getCase(case_id);
  const ans = await extractAnswers(case_);

  // Create new workbook and worksheet
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Case Details");

  // Set up styles
  const headerStyle = {
    font: { bold: true },
    border: {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    },
  };

  const groupHeaderStyle = {
    font: { bold: true, size: 14 },
    fill: {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFD3D3D3" },
    },
  };

  const borderStyle = {
    border: {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    },
  };

  // Add case info header
  worksheet.addRow(["Case Information"]).eachCell((cell) => {
    cell.style = groupHeaderStyle;
  });
  worksheet.mergeCells("A1:B1");

  // Add case details
  worksheet.addRow(["Case Name", case_.name]).eachCell((cell) => {
    cell.style = borderStyle;
  });
  worksheet.addRow(["Description", case_.description]).eachCell((cell) => {
    cell.style = borderStyle;
  });
  worksheet.addRow(["Status", case_.status]).eachCell((cell) => {
    cell.style = borderStyle;
  });
  worksheet.addRow(["Organization", case_.lf_org_name]).eachCell((cell) => {
    cell.style = borderStyle;
  });
  worksheet.addRow(["Phone Number", case_.phone_number]).eachCell((cell) => {
    cell.style = borderStyle;
  });

  // Add answers section
  let rowIndex = worksheet.rowCount + 2;
  ans.forEach((group) => {
    // Add group header
    worksheet.addRow([group.groupName]).eachCell((cell) => {
      cell.style = groupHeaderStyle;
    });
    worksheet.mergeCells(`A${rowIndex}:B${rowIndex}`);
    rowIndex++;

    // Add questions and answers
    group.questions.forEach((question, qIndex) => {
      if (!question.answer) return;

      // Check if the answer is a modal

      worksheet
        .addRow([`Question ${qIndex + 1}`, question.question])
        .eachCell((cell) => {
          cell.style = headerStyle;
        });
      rowIndex++;

      if (
        typeof question.answer === "string" &&
        question.answer.startsWith("[-{+?=;;?+};}{@&,])@}[+@=]=?]")
      ) {
        const modalSheetName = `Modal_${group.groupName}_${qIndex + 1}`;
        worksheet.addRow(["Answer", modalSheetName]).eachCell((cell) => {
          cell.style = borderStyle;
        });
        rowIndex++;

        // Create a new sheet for the modal
        const modalSheet = workbook.addWorksheet(modalSheetName);
        modalSheet.addRow(["Modal Details"]).eachCell((cell) => {
          cell.style = groupHeaderStyle;
        });
        modalSheet.mergeCells("A1:B1");

        const modalLines = question.answer.split("\n");
        modalLines.forEach((line, index) => {
          modalSheet
            .addRow([line.replace("[-{+?=;;?+};}{@&,])@}[+@=]=?]", "")])
            .eachCell((cell) => {
              cell.style = borderStyle;
            });
        });
      } else if (typeof question.answer === "string") {
        worksheet.addRow(["Answer", question.answer]).eachCell((cell) => {
          cell.style = borderStyle;
        });
        rowIndex++;
      } else if (Array.isArray(question.answer)) {
        question.answer.forEach((answer, aIndex) => {
          worksheet
            .addRow([`Answer ${aIndex + 1}`, answer])
            .eachCell((cell) => {
              cell.style = borderStyle;
            });
          rowIndex++;
        });
      }
    });
  });

  // Auto-fit columns
  worksheet.columns.forEach((column) => {
    const lengths = column.values.map((v) => (v ? v.toString().length : 0));
    const maxLength = Math.max(...lengths);
    column.width = Math.min(Math.max(maxLength + 2, 10), 50);
  });

  // Create buffer and return
  const buffer = await workbook.xlsx.writeBuffer();
  // fs.writeFileSync(`case_${case_id}.xlsx`, buffer);
  return buffer;
};

export const getCaseExportConfig = async (caseId) => {
  const con = await pool.getConnection();
  try {
    // Get export config from questionnaire, fallback to template
    const result = await runQuery(con,
      `SELECT q.export_config as qtn_export_config,
              t.export_config as template_export_config
       FROM cases c 
       JOIN questionaires q ON c.qtn_id = q.id 
       JOIN templates t ON q.tem_id = t.id 
       WHERE c.id = ?`,
      [caseId]
    );
    
    if (result.length > 0) {
      // Prefer questionnaire config, fallback to template config
      let config = result[0].qtn_export_config || result[0].template_export_config;
      
      if (config) {
        return typeof config === 'string' ? JSON.parse(config) : config;
      }
    }
    
    // Default config if none found
    return {
      pms_enabled: true,
      data_enabled: true,
      form_enabled: false,
      form_names: ''
    };
  } finally {
    con.destroy();
  }
};

export const exportToFormEvo = async (caseData, formNames) => {
  try {
    // Get case data in PMS format
    let result = await exportDataToPMS(caseData);
    
    // Add form-specific metadata
    result = {
      ...result,
      case_id: caseData.case_id_pms ?? caseData.id,
      lf_id: caseData.lf_id,
      qtn_id: caseData.qtn_id,
      form_names: formNames,
      export_type: 'form'
    };

    // Send to PMS middleware for form processing
    const activePiecesWebhookUrl = process.env.MIDDLEWARE_FORM_TO_PMS || process.env.MIDDLEWARE_DATA_TO_PMS;
    const response = await axios.post(activePiecesWebhookUrl, {
      result,
    });

    return {
      success: true,
      results: formNames.map(formName => ({
        form: formName,
        status: 'success',
        message: `Data exported to ${formName} successfully`
      })),
      pms_response: response.data
    };
  } catch (error) {
    throw ErrorHandler.badRequestError(`Form export failed: ${error.message}`);
  }
};
